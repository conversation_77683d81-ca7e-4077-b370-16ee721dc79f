/**
 * Migration to increase the size of the documents.data column
 * to handle larger error messages and document data
 */

exports.up = function (knex) {
  return knex.schema.alterTable("documents", (table) => {
    // Change data column from TEXT to LONGTEXT to handle larger content
    table.text("data", "longtext").alter();
  });
};

exports.down = function (knex) {
  return knex.schema.alterTable("documents", (table) => {
    // Revert back to regular TEXT
    table.text("data").alter();
  });
};
