/**
 * Migration: Drop providers table and related data
 * 
 * This migration removes the providers table and all related data as part of
 * migrating from database-stored provider configurations to environment variables.
 * 
 * IMPORTANT: This is a destructive migration. Make sure you have:
 * 1. Backed up your database
 * 2. Configured the required environment variables:
 *    - SENDGRID_API_KEY (for email)
 *    - TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, TWILIO_PHONE_NUMBER (for SMS)
 *    - Optional: SMTP_HOST, SMTP_PORT, SMTP_USER, SMTP_PASS (for SMTP email)
 * 3. Tested the global providers functionality
 */

exports.up = async function (knex) {
  console.log('🚀 Starting migration: Drop providers table');
  
  // Check if providers table exists
  const hasProvidersTable = await knex.schema.hasTable('providers');
  if (!hasProvidersTable) {
    console.log('✅ Providers table does not exist, skipping migration');
    return;
  }

  // Log current provider data for reference
  try {
    const providers = await knex('providers').select('*');
    console.log(`📊 Found ${providers.length} provider records to be removed:`);
    
    const providerSummary = providers.reduce((acc, provider) => {
      const key = `${provider.group}-${provider.type}`;
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {});
    
    Object.entries(providerSummary).forEach(([type, count]) => {
      console.log(`   - ${type}: ${count} records`);
    });
    
    // Log sample configurations for reference
    const emailProviders = providers.filter(p => p.group === 'email');
    const textProviders = providers.filter(p => p.group === 'text');
    
    if (emailProviders.length > 0) {
      console.log('📧 Email provider configurations found:');
      emailProviders.forEach(p => {
        const data = typeof p.data === 'string' ? JSON.parse(p.data) : p.data;
        if (p.type === 'sendgrid' && data.api_key) {
          console.log(`   - SendGrid: ${data.api_key.slice(0, 10)}...${data.api_key.slice(-10)}`);
        } else if (p.type === 'smtp' && data.host) {
          console.log(`   - SMTP: ${data.user}@${data.host}:${data.port}`);
        }
      });
    }
    
    if (textProviders.length > 0) {
      console.log('📱 SMS provider configurations found:');
      textProviders.forEach(p => {
        const data = typeof p.data === 'string' ? JSON.parse(p.data) : p.data;
        if (p.type === 'twilio' && data.account_sid) {
          console.log(`   - Twilio: ${data.account_sid.slice(0, 10)}...${data.account_sid.slice(-4)} (${data.phone_number})`);
        }
      });
    }
  } catch (error) {
    console.log('⚠️  Could not read provider data:', error.message);
  }

  console.log('🗑️  Handling foreign key constraints and dropping providers table...');

  // First, find all tables that reference the providers table
  let foreignKeys;
  try {
    foreignKeys = await knex.raw(`
      SELECT
        TABLE_NAME,
        COLUMN_NAME,
        CONSTRAINT_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
      WHERE REFERENCED_TABLE_NAME = 'providers'
    `);
  } catch (error) {
    console.log('⚠️  Could not query foreign keys, proceeding with manual cleanup');
    foreignKeys = [[]]; // Empty result set
  }

  console.log(`📋 Found ${foreignKeys[0].length} foreign key constraint(s) referencing providers table:`);

  // If no foreign keys found automatically, try manual cleanup of known tables
  if (foreignKeys[0].length === 0) {
    console.log('🔧 No foreign keys detected automatically, trying manual cleanup...');

    // Check if campaigns table has provider_id column
    const campaignsColumns = await knex('information_schema.columns')
      .where({ table_name: 'campaigns', column_name: 'provider_id' })
      .first();

    if (campaignsColumns) {
      console.log('🔧 Found provider_id column in campaigns table, removing...');
      try {
        // Try to drop any foreign key constraints on provider_id
        await knex.raw('ALTER TABLE campaigns DROP FOREIGN KEY campaigns_provider_id_foreign');
        console.log('   ✅ Dropped campaigns_provider_id_foreign constraint');
      } catch (error) {
        console.log(`   ⚠️  Could not drop foreign key constraint: ${error.message}`);
      }

      try {
        // Make provider_id column nullable instead of dropping it
        // This allows existing campaigns to keep their provider_id values
        // while new campaigns can have null provider_id
        await knex.schema.alterTable('campaigns', (table) => {
          table.integer('provider_id').nullable().alter();
        });
        console.log('   ✅ Made provider_id column nullable in campaigns table');
      } catch (error) {
        console.log(`   ⚠️  Could not make provider_id column nullable: ${error.message}`);
      }
    }
  } else {
    // Handle each foreign key constraint found automatically
    for (const fk of foreignKeys[0]) {
      console.log(`   - ${fk.TABLE_NAME}.${fk.COLUMN_NAME} → providers.${fk.REFERENCED_COLUMN_NAME} (${fk.CONSTRAINT_NAME})`);

      if (fk.TABLE_NAME === 'campaigns') {
        console.log('🔧 Updating campaigns table to remove provider_id dependency...');

        try {
        // Drop the foreign key constraint
          await knex.schema.alterTable('campaigns', (table) => {
            table.dropForeign('provider_id', fk.CONSTRAINT_NAME);
          });
          console.log(`   ✅ Dropped foreign key constraint: ${fk.CONSTRAINT_NAME}`);
        } catch (error) {
          console.log(`   ⚠️  Could not drop foreign key constraint: ${error.message}`);
        }

        try {
          // Make provider_id column nullable instead of dropping it
          // This allows existing campaigns to keep their provider_id values
          // while new campaigns can have null provider_id
          await knex.schema.alterTable('campaigns', (table) => {
            table.integer('provider_id').nullable().alter();
          });
          console.log('   ✅ Made provider_id column nullable in campaigns table');
        } catch (error) {
          console.log(`   ⚠️  Could not make provider_id column nullable: ${error.message}`);
        }
      } else {
      // Handle other tables that might reference providers
        console.log(`⚠️  Found unexpected foreign key from ${fk.TABLE_NAME}.${fk.COLUMN_NAME}`);
        console.log(`   Dropping foreign key constraint: ${fk.CONSTRAINT_NAME}`);

        try {
          await knex.schema.alterTable(fk.TABLE_NAME, (table) => {
            table.dropForeign(fk.COLUMN_NAME, fk.CONSTRAINT_NAME);
          });
          console.log(`   ✅ Dropped foreign key constraint: ${fk.CONSTRAINT_NAME}`);

          // Make the column nullable instead of dropping it
          await knex.schema.alterTable(fk.TABLE_NAME, (table) => {
            table.integer(fk.COLUMN_NAME).nullable().alter();
          });
          console.log(`   ✅ Made ${fk.COLUMN_NAME} column nullable`);
        } catch (error) {
          console.log(`   ⚠️  Could not handle foreign key: ${error.message}`);
        }
      }
    } // End of else block for handling foreign keys
  } // <-- Add this closing brace for exports.up

  // Now we can safely drop the providers table
  await knex.schema.dropTableIfExists('providers');
  
  console.log('✅ Successfully dropped providers table');
  console.log('');
  console.log('🔧 IMPORTANT: Make sure you have configured these environment variables:');
  console.log('   Email (SendGrid): SENDGRID_API_KEY');
  console.log('   Email (SMTP): SMTP_HOST, SMTP_PORT, SMTP_USER, SMTP_PASS');
  console.log('   SMS (Twilio): TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, TWILIO_PHONE_NUMBER');
  console.log('');
  console.log('🎉 Migration completed successfully!');
};

exports.down = function (knex) {
  console.log('❌ This migration cannot be reversed automatically.');
  console.log('   The providers table and data have been permanently removed.');
  console.log('   To restore provider functionality, you would need to:');
  console.log('   1. Restore the providers table schema from a backup');
  console.log('   2. Restore the provider data from a backup');
  console.log('   3. Revert the code changes to use database providers');
  console.log('');
  console.log('   It is recommended to use environment variables instead.');
  
  // We could recreate the table structure, but without the data it would be useless
  // Better to fail explicitly and require manual intervention
  throw new Error('This migration cannot be automatically reversed. Please restore from backup if needed.');
};
