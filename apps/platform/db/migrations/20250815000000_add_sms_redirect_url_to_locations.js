exports.up = async function (knex) {
  const hasColumn = await knex.schema.hasColumn('locations', 'sms_redirect_url');
  if (!hasColumn) {
    await knex.schema.alterTable('locations', function (table) {
      table.string('sms_redirect_url', 255).nullable();
    });
  }
};

exports.down = async function (knex) {
  const hasColumn = await knex.schema.hasColumn('locations', 'sms_redirect_url');
  if (hasColumn) {
    await knex.schema.alterTable('locations', function (table) {
      table.dropColumn('sms_redirect_url');
    });
  }
};

