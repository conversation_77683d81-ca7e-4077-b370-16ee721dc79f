exports.up = async function (knex) {
  const exists = await knex.schema.hasTable("sms_history");
  if (exists) return;
  await knex.schema.createTable("sms_history", function (table) {
    table.increments();
    table
      .integer("location_id")
      .unsigned()
      .notNullable()
      .references("id")
      .inTable("locations")
      .onDelete("CASCADE");
    table
      .integer("admin_id")
      .unsigned()
      .nullable()
      .references("id")
      .inTable("admins")
      .onDelete("SET NULL");
    table
      .integer("user_id")
      .unsigned()
      .notNullable()
      .references("id")
      .inTable("users")
      .onDelete("CASCADE");
    table
      .integer("campaign_id")
      .unsigned()
      .nullable()
      .references("id")
      .inTable("campaigns")
      .onDelete("SET NULL");

    // insight_id is optional and may not always have a foreign key; keep as plain integer
    table.integer("insight_id").unsigned().nullable();

    table.string("phone", 32).notNullable();
    table.string("provider", 50).notNullable().defaultTo("leafbuyer");
    table.string("type", 8).notNullable().defaultTo("SMS"); // SMS or MMS
    table.string("status", 50).nullable();
    table.string("dispatch_id", 128).nullable();
    table.integer("template_id").unsigned().nullable();
    table.text("message").nullable();
    table.json("response").nullable();
    table.timestamp("sent_at").defaultTo(knex.fn.now()).index();

    table.timestamp("created_at").defaultTo(knex.fn.now());
    table.timestamp("updated_at").defaultTo(knex.fn.now());

    table.index(["location_id", "sent_at"], "idx_sms_history_location_sent");
    table.index(["user_id"], "idx_sms_history_user");
    table.index(["campaign_id"], "idx_sms_history_campaign");
    table.index(["insight_id"], "idx_sms_history_insight");
    table.index(["dispatch_id"], "idx_sms_history_dispatch");
  });
};

exports.down = async function (knex) {
  await knex.schema.dropTableIfExists("sms_history");
};
