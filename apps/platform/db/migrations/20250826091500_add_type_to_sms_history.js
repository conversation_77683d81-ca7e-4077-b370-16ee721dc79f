exports.up = async function (knex) {
  const hasTable = await knex.schema.hasTable("sms_history");
  if (!hasTable) return;

  const hasColumn = await knex.schema.hasColumn("sms_history", "type");
  if (!hasColumn) {
    await knex.schema.alterTable("sms_history", function (table) {
      table.string("type", 8).notNullable().defaultTo("SMS");
    });

    // Backfill any existing NULLs just in case
    await knex("sms_history").whereNull("type").update({ type: "SMS" });
  }
};

exports.down = async function (knex) {
  const hasTable = await knex.schema.hasTable("sms_history");
  if (!hasTable) return;

  const hasColumn = await knex.schema.hasColumn("sms_history", "type");
  if (hasColumn) {
    await knex.schema.alterTable("sms_history", function (table) {
      table.dropColumn("type");
    });
  }
};

