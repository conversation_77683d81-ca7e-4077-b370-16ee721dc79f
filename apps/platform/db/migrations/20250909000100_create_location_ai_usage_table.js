exports.up = async function (knex) {
  const exists = await knex.schema.hasTable("location_ai_usage");
  if (!exists) {
    await knex.schema.createTable("location_ai_usage", function (table) {
      table.increments();
      table
        .integer("location_id")
        .unsigned()
        .notNullable()
        .references("id")
        .inTable("locations")
        .onDelete("CASCADE");
      table.integer("used").notNullable().defaultTo(0);
      table.timestamp("created_at").defaultTo(knex.fn.now());
      table.timestamp("updated_at").defaultTo(knex.fn.now());
      table.unique(["location_id"]);
    });
  }
};

exports.down = async function (knex) {
  const exists = await knex.schema.hasTable("location_ai_usage");
  if (exists) {
    await knex.schema.dropTable("location_ai_usage");
  }
};

