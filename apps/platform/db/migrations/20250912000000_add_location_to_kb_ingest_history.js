exports.up = async function (knex) {
  const hasTable = await knex.schema.hasTable("kb_ingest_history");
  if (!hasTable) return;

  const hasCol = await knex.schema.hasColumn("kb_ingest_history", "location_id");
  if (!hasCol) {
    await knex.schema.alterTable("kb_ingest_history", function (table) {
      table.integer("location_id").unsigned().nullable().index();
    });
  }

  // Optional composite index to speed up org+location queries
  try {
    await knex.schema.alterTable("kb_ingest_history", function (table) {
      table.index(["organization_id", "location_id", "created_at"], "kb_ingest_history_org_loc_created_idx");
    });
  } catch (e) {
    // Index may already exist; ignore
  }
};

exports.down = async function (knex) {
  const hasTable = await knex.schema.hasTable("kb_ingest_history");
  if (!hasTable) return;

  const hasCol = await knex.schema.hasColumn("kb_ingest_history", "location_id");
  if (hasCol) {
    await knex.schema.alterTable("kb_ingest_history", function (table) {
      table.dropIndex(["organization_id", "location_id", "created_at"], "kb_ingest_history_org_loc_created_idx");
      table.dropColumn("location_id");
    });
  }
};

