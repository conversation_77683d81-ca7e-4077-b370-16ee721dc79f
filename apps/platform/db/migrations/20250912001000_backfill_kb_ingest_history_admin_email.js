exports.up = async function (knex) {
  // Backfill admin_email from admins table when missing or placeholder (portable SQL via subquery)
  await knex.raw(`
    UPDATE kb_ingest_history AS h
    SET admin_email = (
      SELECT a.email FROM admins AS a WHERE a.id = h.admin_id LIMIT 1
    )
    WHERE (h.admin_email IS NULL OR h.admin_email = '' OR h.admin_email = 'unknown');
  `);
};

exports.down = async function (_knex) {
  // No-op: cannot reliably revert data backfill
};

