exports.up = async function (knex) {
  const hasTable = await knex.schema.hasTable("cannmenus_api_calls");
  if (hasTable) return;

  await knex.schema.createTable("cannmenus_api_calls", function (table) {
    table.increments("id").primary();
    table.integer("location_id").unsigned().nullable().references("id").inTable("locations").onDelete("SET NULL").index();
    table.string("endpoint", 255).notNullable();
    table.string("method", 8).notNullable().defaultTo("GET");
    table.json("params").nullable();
    table.integer("status").nullable();
    table.boolean("success").notNullable().defaultTo(false);
    table.integer("response_count").nullable();
    table.integer("duration_ms").nullable();
    table.text("error").nullable();
    table.timestamp("created_at").defaultTo(knex.fn.now());
    table.timestamp("updated_at").defaultTo(knex.fn.now());

    table.index(["location_id", "created_at"], "cannmenus_calls_loc_created_idx");
    table.index(["endpoint", "created_at"], "cannmenus_calls_endpoint_created_idx");
  });
};

exports.down = async function (knex) {
  const hasTable = await knex.schema.hasTable("cannmenus_api_calls");
  if (!hasTable) return;
  await knex.schema.dropTable("cannmenus_api_calls");
};

