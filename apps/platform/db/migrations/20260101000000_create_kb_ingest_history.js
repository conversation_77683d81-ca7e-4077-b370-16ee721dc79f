exports.up = async function (knex) {
  const hasTable = await knex.schema.hasTable("kb_ingest_history");
  if (hasTable) return;

  await knex.schema.createTable("kb_ingest_history", function (table) {
    table.increments();
    table.integer("organization_id").unsigned().notNullable().index();
    table.integer("admin_id").unsigned().notNullable().index();
    table.string("admin_email", 255).notNullable();
    table.string("url", 2048).notNullable();
    table.boolean("ok").notNullable().defaultTo(false);
    table.integer("text_length").unsigned().nullable();
    table.text("error").nullable();
    table.timestamp("created_at").defaultTo(knex.fn.now());

    table.index(["organization_id", "created_at"], "kb_ingest_history_org_created_idx");
  });
};

exports.down = async function (knex) {
  const hasTable = await knex.schema.hasTable("kb_ingest_history");
  if (!hasTable) return;
  await knex.schema.dropTable("kb_ingest_history");
};

