import Model from "../core/Model";

export default class CannMenusApiCall extends Model {
  declare id: number;
  location_id?: number | null;
  endpoint!: string;
  method!: string;
  params?: any;
  status?: number | null;
  success!: boolean;
  response_count?: number | null;
  duration_ms?: number | null;
  error?: string | null;
  declare created_at: Date;
  declare updated_at: Date;

  static get tableName() {
    return "cannmenus_api_calls";
  }
}
