export interface RankedItem<T> {
  id: string;
  score?: number; // higher is better; may be undefined
  rank: number; // 1-based rank position within its list
  payload: T;
}

/**
 * Reciprocal Rank Fusion implementation
 * Given multiple ranked lists, fuse with RRF: score = sum(1 / (k + rank))
 * Default k=60 works well to balance head vs tail per literature.
 */
export function reciprocalRankFusion<T>(
  lists: Array<Array<RankedItem<T>>>,
  k: number = 60,
  limit: number = 10
): Array<RankedItem<T> & { fused: number }> {
  const fused: Map<string, { item: RankedItem<T>; fused: number }> = new Map();

  for (const list of lists) {
    for (const it of list) {
      const contribution = 1 / (k + it.rank);
      const ex = fused.get(it.id);
      if (!ex) fused.set(it.id, { item: it, fused: contribution });
      else ex.fused += contribution;
    }
  }

  const arr = Array.from(fused.values())
    .sort((a, b) => b.fused - a.fused)
    .slice(0, limit)
    .map(({ item, fused }) => ({ ...item, fused }));

  return arr;
}
