import { Tool as <PERSON><PERSON><PERSON><PERSON><PERSON>ool } from "@langchain/core/tools";
import { logger } from "../../config/logger";
import { DocumentVectorService } from "../../documents/DocumentVectorService";

export class DocumentVectorSearchTool extends Lang<PERSON>hainTool {
  name = "document_vector_search";
  description =
    "Search uploaded documents for relevant passages using the Mongo-backed vector store. Provide a natural language query and the current locationId.";

  protected async _call(input: string | Record<string, any>): Promise<string> {
    try {
      let params: any;
      if (typeof input === "string") {
        try {
          params = JSON.parse(input);
        } catch {
          return JSON.stringify({ status: "error", error: "Invalid JSON input" });
        }
      } else {
        params = input || {};
      }

      const query = params.query as string;
      const locationId = Number(params.locationId);
      const topK = params.topK !== undefined ? Number(params.topK) : 5;

      if (!query || typeof query !== "string") {
        return JSON.stringify({ status: "error", error: "query is required" });
      }
      if (!locationId || isNaN(locationId)) {
        return JSON.stringify({
          status: "error",
          error: "locationId is required and must be a number",
        });
      }

      const limit = Math.min(Math.max(topK ?? 5, 1), 20);

      logger.info("DocumentVectorSearchTool invoked", { query, locationId, limit });

      const chunks = await DocumentVectorService.searchDocumentChunks(
        query,
        locationId,
        limit
      );

      const results = chunks.map((chunk) => ({
        id: chunk.id,
        content: chunk.content,
        metadata: chunk.metadata,
        source: `${chunk.metadata.document_name || "Document"}${
          chunk.metadata.page ? ` (p.${chunk.metadata.page})` : ""
        }`,
        type: "document",
      }));

      return JSON.stringify({
        status: "success",
        data: {
          results,
          query_info: {
            original_query: query,
            location_id: locationId,
            total_results: results.length,
            using_service: "DocumentVectorService",
          },
        },
      });
    } catch (error) {
      logger.error("DocumentVectorSearchTool error", error);
      return JSON.stringify({
        status: "error",
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }
}
