import Router from "@koa/router";
import { authMiddleware, scopeMiddleware } from "../auth/AuthMiddleware";
import App from "../app";
import { knowledgeBaseService } from "../kb/KnowledgeBaseService";
import { reciprocalRankFusion, RankedItem } from "../chats/RRFUtil";

const KnowledgeBaseController = (_app: App) => {
  // Mounted under admin router (prefix "/admin"), so use relative prefix here
  const router = new Router({ prefix: "/kb" });
  router.use(authMiddleware);
  router.use(scopeMiddleware(["admin", "secret"]));

  // Ingest URLs into bakedbot_kb and log history per URL
  router.post("/ingest-urls", async (ctx) => {
    const { urls, location_id } = ctx.request.body as any;
    if (!urls || !Array.isArray(urls)) {
      ctx.status = 400;
      ctx.body = { error: "Body must include 'urls' array" };
      return;
    }

    const admin = ctx.state.admin as any;
    const adminEmail = (admin?.email || "").toLowerCase();
    const superEmails = (process.env.SUPER_USER_EMAILS || "")
      .split(",")
      .map((s) => s.trim().toLowerCase())
      .filter(Boolean);
    const isSuper = superEmails.includes(adminEmail);

    // Normalize and dedupe
    const normalized = Array.from(new Set(urls.map((u: any) => String(u).trim()).filter(Boolean)));

    const result = await knowledgeBaseService.ingestUrls(normalized, {
      locationId: isSuper ? undefined : (location_id ? parseInt(location_id, 10) : undefined),
      superUser: isSuper,
    });
    ctx.body = result;

    // Best-effort: store ingest history
    try {
      const orgId = admin?.organization_id;
      const adminId = admin?.id;
      if (orgId && adminId && Array.isArray(result?.details)) {
        const rows = (result.details as any[]).map((d) => ({
          organization_id: orgId,
          admin_id: adminId,
          admin_email: admin?.email || "system",
          url: d.url,
          ok: !!d.ok,
          text_length: d.text_length || null,
          error: d.error || null,
          location_id: isSuper ? null : (location_id ? parseInt(location_id, 10) : null),
        }));
        // Filter out entries marked as skipped (no change since last ingest)
        const filtered = rows.filter((d: any, i: number) => {
          const detail = (result.details as any[])[i];
          return !(detail && detail.skipped === true);
        });
        if (filtered.length > 0) {
          await App.main.db.table("kb_ingest_history").insert(filtered);
        }
      }
    } catch (logErr) {
      // Non-blocking
    }
  });

  // Search Knowledge Base: ALWAYS include global KB, PLUS location KB when provided; fuse with RRF
  router.get("/search", async (ctx) => {
    const q = (ctx.query.q as string) || "";
    const topK = parseInt((ctx.query.topK as string) || "5");
    const locationIdParam = ctx.query.location_id ? parseInt(ctx.query.location_id as string, 10) : undefined;

    const [globalResults, localResults] = await Promise.all([
      knowledgeBaseService.search(q, topK, { superUser: true }),
      locationIdParam ? knowledgeBaseService.search(q, topK, { locationId: locationIdParam }) : Promise.resolve([]),
    ]);

    const globalRanked = (globalResults as any[]).map((r, idx) => ({ id: r.id, rank: idx + 1, payload: { scope: "global", item: r } })) as RankedItem<any>[];
    const localRanked = (localResults as any[]).map((r, idx) => ({ id: r.id, rank: idx + 1, payload: { scope: "location", item: r } })) as RankedItem<any>[];
    const fused = reciprocalRankFusion([globalRanked, localRanked], 60, topK);
    const results = fused.map((f) => ({ ...f.payload.item, fused: f.fused, scope: f.payload.scope }));

    ctx.body = { results };
  });

  // List ingest history (most recent first)
  router.get("/history", async (ctx) => {
    const admin = ctx.state.admin as any;
    const orgId = admin?.organization_id;
    if (!orgId) {
      ctx.status = 400;
      ctx.body = { error: "Missing organization context" };
      return;
    }
    const limit = Math.min(parseInt((ctx.query.limit as string) || "50", 10), 200);
    const offset = Math.max(parseInt((ctx.query.offset as string) || "0", 10), 0);
    const q = ((ctx.query.q as string) || "").trim().toLowerCase();

    const adminEmail = (admin?.email || "").toLowerCase();
    const superEmails = (process.env.SUPER_USER_EMAILS || "")
      .split(",")
      .map((s) => s.trim().toLowerCase())
      .filter(Boolean);
    const isSuper = superEmails.includes(adminEmail);
    const locationIdParam = ctx.query.location_id ? parseInt(ctx.query.location_id as string, 10) : undefined;

    let qb = App.main.db
      .table("kb_ingest_history as h")
      .leftJoin("admins as a", "h.admin_id", "a.id")
      .where({ "h.organization_id": orgId });
    if (!isSuper) {
      // For non-super users, constrain by location when provided
      if (locationIdParam) qb = qb.andWhere("h.location_id", locationIdParam);
      else qb = qb.whereNull("h.location_id"); // if no location provided, show only global entries (unlikely)
    }
    if (q) {
      qb = qb.andWhereRaw("LOWER(h.url) LIKE ?", [`%${q}%`]);
    }

    const rowsRaw = await qb
      .select("h.*", App.main.db.raw("COALESCE(h.admin_email, a.email) as admin_email_filled"))
      .orderBy("h.created_at", "desc")
      .limit(limit)
      .offset(offset);

    const rows = rowsRaw.map((r: any) => {
      const { admin_email_filled, ...rest } = r;
      return { ...rest, admin_email: admin_email_filled || "system" };
    });

    ctx.body = { results: rows, scope: isSuper ? "global" : "location" };
  });

  // Check last processed for a given URL
  router.get("/check", async (ctx) => {
    const admin = ctx.state.admin as any;
    const orgId = admin?.organization_id;
    if (!orgId) {
      ctx.status = 400;
      ctx.body = { error: "Missing organization context" };
      return;
    }
    const url = String(ctx.query.url || "").trim();
    if (!url) {
      ctx.status = 400;
      ctx.body = { error: "Missing url" };
      return;
    }

    const adminEmail = (admin?.email || "").toLowerCase();
    const superEmails = (process.env.SUPER_USER_EMAILS || "")
      .split(",")
      .map((s) => s.trim().toLowerCase())
      .filter(Boolean);
    const isSuper = superEmails.includes(adminEmail);
    const locationIdParam = ctx.query.location_id ? parseInt(ctx.query.location_id as string, 10) : undefined;

    let qb = App.main.db
      .table("kb_ingest_history as h")
      .leftJoin("admins as a", "h.admin_id", "a.id")
      .where({ "h.organization_id": orgId, "h.url": url });
    if (!isSuper) {
      if (locationIdParam) qb = qb.andWhere("h.location_id", locationIdParam);
      else qb = qb.whereNull("h.location_id");
    }

    const rowRaw = await qb
      .select("h.*", App.main.db.raw("COALESCE(h.admin_email, a.email) as admin_email_filled"))
      .orderBy("h.created_at", "desc")
      .first();

    const row = rowRaw
      ? { ...rowRaw, admin_email: rowRaw.admin_email_filled || "system" }
      : null;

    ctx.body = { last: row };
  });

  return router;
};

export default KnowledgeBaseController as any;
