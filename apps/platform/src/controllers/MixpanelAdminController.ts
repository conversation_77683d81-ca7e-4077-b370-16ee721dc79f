import Router from "@koa/router";
import { Context } from "koa";
import { getMixpanelClient } from "../config/analytics";
import { superUserMiddleware } from "../auth/SuperUserService";
import { authMiddleware, scopeMiddleware } from "../auth/AuthMiddleware";
import { organizationMiddleware } from "../organizations/OrganizationMiddleware";
import { validate } from "../core/validate";
import { z } from "zod";
import { JSONSchemaType } from "ajv";
const router = new Router({ prefix: "/mixpanel-admin" });

// Apply middleware to all routes
router.use(authMiddleware);
router.use(organizationMiddleware);
router.use(scopeMiddleware("admin"));
router.use(superUserMiddleware);

// Validation schemas

const trackEventSchema: JSONSchemaType<{
  userId: string;
  eventName: string;
  properties?: Record<string, any>;
}> = {
  type: "object",
  required: ["userId", "eventName"],
  properties: {
    userId: { type: "string", minLength: 1 },
    eventName: { type: "string", minLength: 1 },
    properties: {
      type: "object",
      additionalProperties: true,
      nullable: true,
      default: {},
    },
  },
  additionalProperties: false,
};

const setPropertiesSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
  properties: z.record(z.any()).refine(obj => Object.keys(obj).length > 0, {
    message: "At least one property is required"
  }),
});

const incrementPropertySchema = z.object({
  userId: z.string().min(1, "User ID is required"),
  property: z.string().min(1, "Property name is required"),
  value: z.number().optional().default(1),
});

/**
 * @swagger
 * /api/mixpanel-admin/track:
 *   post:
 *     summary: Track a Mixpanel event (Super User Only)
 *     tags: [Mixpanel Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - eventName
 *             properties:
 *               userId:
 *                 type: string
 *                 description: The user ID to track the event for
 *               eventName:
 *                 type: string
 *                 description: The name of the event to track
 *               properties:
 *                 type: object
 *                 description: Additional properties for the event
 *     responses:
 *       200:
 *         description: Event tracked successfully
 *       400:
 *         description: Invalid request data
 *       403:
 *         description: Super user access required
 *       500:
 *         description: Mixpanel not initialized or tracking failed
 */
router.post("/track", async (ctx: Context) => {
  const { userId, eventName, properties } = validate(trackEventSchema, ctx.request.body);
  
  const mixpanel = getMixpanelClient();
  if (!mixpanel) {
    ctx.status = 500;
    ctx.body = { error: "Mixpanel client not initialized" };
    return;
  }

  try {
    mixpanel.track(eventName, {
      distinct_id: userId,
      ...properties,
      timestamp: new Date().toISOString(),
      tracked_by: "admin_interface",
      admin_email: ctx.state.admin?.email || "unknown",
    });

    ctx.body = { 
      success: true, 
      message: `Event '${eventName}' tracked for user '${userId}'` 
    };
  } catch (error) {
    ctx.status = 500;
    ctx.body = { error: "Failed to track event", details: error };
  }
});

/**
 * @swagger
 * /api/mixpanel-admin/properties:
 *   post:
 *     summary: Set user properties in Mixpanel (Super User Only)
 *     tags: [Mixpanel Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - properties
 *             properties:
 *               userId:
 *                 type: string
 *                 description: The user ID to set properties for
 *               properties:
 *                 type: object
 *                 description: The properties to set for the user
 *     responses:
 *       200:
 *         description: Properties set successfully
 *       400:
 *         description: Invalid request data
 *       403:
 *         description: Super user access required
 *       500:
 *         description: Mixpanel not initialized or operation failed
 */
router.post("/properties", async (ctx: Context) => {
  const { userId, properties } = setPropertiesSchema.parse(ctx.request.body);

  const mixpanel = getMixpanelClient();
  if (!mixpanel) {
    ctx.status = 500;
    ctx.body = { error: "Mixpanel client not initialized" };
    return;
  }

  try {
    // Add metadata about who set these properties
    const enhancedProperties = {
      ...properties,
      $last_updated_by: "admin_interface",
      $last_updated_admin: ctx.state.admin?.email || "unknown",
      $last_updated_at: new Date().toISOString(),
    };

    mixpanel.people.set(userId, enhancedProperties);

    ctx.body = { 
      success: true, 
      message: `Properties set for user '${userId}'`,
      properties: Object.keys(properties)
    };
  } catch (error) {
    ctx.status = 500;
    ctx.body = { error: "Failed to set properties", details: error };
  }
});

/**
 * @swagger
 * /api/mixpanel-admin/increment:
 *   post:
 *     summary: Increment a user property in Mixpanel (Super User Only)
 *     tags: [Mixpanel Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - property
 *             properties:
 *               userId:
 *                 type: string
 *                 description: The user ID to increment property for
 *               property:
 *                 type: string
 *                 description: The property name to increment
 *               value:
 *                 type: number
 *                 description: "The value to increment by (default: 1)"
 *     responses:
 *       200:
 *         description: Property incremented successfully
 *       400:
 *         description: Invalid request data
 *       403:
 *         description: Super user access required
 *       500:
 *         description: Mixpanel not initialized or operation failed
 */
router.post("/increment", async (ctx: Context) => {
  const { userId, property, value } = incrementPropertySchema.parse(ctx.request.body);
  
  const mixpanel = getMixpanelClient();
  if (!mixpanel) {
    ctx.status = 500;
    ctx.body = { error: "Mixpanel client not initialized" };
    return;
  }

  try {
    mixpanel.people.increment(userId, property, value);

    ctx.body = { 
      success: true, 
      message: `Property '${property}' incremented by ${value} for user '${userId}'` 
    };
  } catch (error) {
    ctx.status = 500;
    ctx.body = { error: "Failed to increment property", details: error };
  }
});

/**
 * @swagger
 * /api/mixpanel-admin/status:
 *   get:
 *     summary: Get Mixpanel integration status (Super User Only)
 *     tags: [Mixpanel Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Mixpanel status information
 *       403:
 *         description: Super user access required
 */
router.get("/status", async (ctx: Context) => {
  const mixpanel = getMixpanelClient();
  
  ctx.body = {
    initialized: !!mixpanel,
    timestamp: new Date().toISOString(),
    admin: ctx.state.admin?.email || "unknown",
  };
});

export default router;
