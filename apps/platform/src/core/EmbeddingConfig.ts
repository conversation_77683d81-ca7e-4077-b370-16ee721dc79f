// Centralized embedding configuration to avoid drift across services
// Default to text-embedding-3-small (1536 dims) for Atlas compatibility (<= 2048)

export const EMBEDDING_MODEL =
  process.env.OPENAI_EMBEDDING_MODEL || "text-embedding-3-small";

export function getEmbeddingDimensions(model: string = EMBEDDING_MODEL): number {
  // Map known OpenAI embedding models to dimensions
  // text-embedding-3-small: 1536, text-embedding-3-large: 3072
  if (model.includes("text-embedding-3-large")) return 3072;
  if (model.includes("text-embedding-3-small")) return 1536;
  // Sensible default
  return 1536;
}

export const EMBEDDING_DIMENSIONS = getEmbeddingDimensions();
