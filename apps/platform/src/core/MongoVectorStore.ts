import { MongoClient, Db, Collection, Document, WithId } from "mongodb";
import { logger } from "../config/logger";
import * as fs from "fs";
import * as os from "os";
import * as path from "path";

export interface MongoVectorDoc {
  _id: string; // vector id
  embedding: number[];
  metadata: Record<string, any>;
  namespace?: string; // optional namespace
  created_at?: number;
  updated_at?: number;
}

export class MongoVectorStore {
  async deleteByFilter(indexName: string, filter: Record<string, any>, namespace?: string) {
    const q = this.buildFilter(filter, namespace) || {};
    await this.collection(indexName).deleteMany(q as any);
  }

  async exists(indexName: string, filter: Record<string, any>, namespace?: string): Promise<boolean> {
    const q = this.buildFilter(filter, namespace) || {};
    const count = await this.collection(indexName).countDocuments(q as any, { limit: 1 } as any);
    return count > 0;
  }

  private client!: MongoClient;
  private db!: Db;
  private vectorIndexName: string;
  private initialized = false;

  constructor(private uri: string, private dbName: string, options?: { indexName?: string }) {
    this.vectorIndexName = options?.indexName || process.env.MONGODB_VECTOR_INDEX_NAME || "vector_index";
  }

  async initialize() {
    if (this.initialized) return;
    const masked = this.uri.replace(/:\/\/(.*?):(.*?)@/, "://****:****@");
    logger.info({ mongoUri: masked, dbName: this.dbName }, "Initializing MongoVectorStore");
    // TLS/SSL options for Render/Atlas environments can be controlled via env
    const tls = process.env.MONGODB_TLS === "true" || false;
    const tlsInsecure = process.env.MONGODB_TLS_INSECURE === "true" || false; // allowInvalidCertificates
    const caFile = process.env.MONGODB_CA_FILE; // optional path to CA cert

    const mongoOptions: any = { ignoreUndefined: true };
    if (tls) mongoOptions.tls = true;
    if (tlsInsecure) mongoOptions.tlsAllowInvalidCertificates = true;
    if (caFile) mongoOptions.tlsCAFile = caFile;

    this.client = new MongoClient(this.uri, mongoOptions);
    await this.client.connect();
    this.db = this.client.db(this.dbName);
    this.initialized = true;
    logger.info("MongoVectorStore initialized");
  }

  private collection(indexName: string): Collection<MongoVectorDoc> {
    return this.db.collection<MongoVectorDoc>(indexName);
  }

  async ensureIndex(indexName: string, dimension: number) {
    // Create collection if not exists by touching it
    await this.collection(indexName).insertOne({ _id: `__init__${Date.now()}`, embedding: new Array(dimension).fill(0), metadata: { init: true }, created_at: Date.now(), updated_at: Date.now() }).catch(() => {});
    await this.collection(indexName).deleteOne({ _id: { $regex: "^__init__" } as any });

    // Try to create Atlas Vector Search index if supported. Non-fatal on failure.
    try {
      const cmd: any = {
        createSearchIndexes: indexName,
        indexes: [
          {
            name: this.vectorIndexName,
            definition: {
              mappings: {
                dynamic: false,
                fields: {
                  embedding: {
                    type: "knnVector",
                    dimensions: dimension,
                    similarity: "cosine",
                  },
                  // Namespace must be tokenized to be usable in $vectorSearch filters
                  namespace: { type: "token" },
                  // Metadata fields are dynamic; keep them keyword/text at query time via filter stages
                },
              },
            },
          },
        ],
      };
      // @ts-ignore command may not exist on self-hosted
      await (this.db as any).command(cmd);
      logger.info({ indexName, searchIndex: this.vectorIndexName }, "Ensured Atlas Vector Search index");
    } catch (err: any) {
      logger.warn({ indexName, error: err?.message }, "Could not create Atlas Vector Search index automatically. If using Atlas, create a search index named '" + this.vectorIndexName + "' on collection '" + indexName + "' with vector path 'embedding'.");
    }
  }

  async listIndices(): Promise<string[]> {
    const cols = await this.db.listCollections().toArray();
    return cols.map((c) => c.name);
  }

  async deleteIndex(indexName: string): Promise<void> {
    await this.collection(indexName).drop().catch(() => {});
  }

  async emptyIndex(indexName: string): Promise<void> {
    await this.collection(indexName).deleteMany({});
  }

  async upsert(indexName: string, docs: MongoVectorDoc[], namespace?: string) {
    const now = Date.now();
    const ops = docs.map((d) => ({
      updateOne: {
        filter: { _id: d._id },
        update: {
          $set: {
            embedding: d.embedding,
            metadata: d.metadata,
            namespace: namespace ?? d.namespace,
            updated_at: now,
          },
          $setOnInsert: { created_at: now },
        },
        upsert: true,
      },
    }));
    if (ops.length === 0) return { matchedCount: 0, upsertedCount: 0 };
    const res = await this.collection(indexName).bulkWrite(ops, { ordered: false });
    return { matchedCount: res.matchedCount, upsertedCount: res.upsertedCount };
  }

  async query(indexName: string, queryVector: number[], filter: Record<string, any> | undefined, topK: number, namespace?: string) {
    // Build a post-search match filter to avoid Atlas requirement of token indexing on metadata.* fields
    const matchClause = this.buildFilter(filter, namespace);

    const vectorStage: any = {
      $vectorSearch: {
        index: this.vectorIndexName,
        path: "embedding",
        queryVector,
        numCandidates: Math.max(100, topK * 20),
        limit: topK,
        // Do not pass filter here; apply as $match after to avoid "needs to be indexed as token" errors
      },
    };

    const projectStage = {
      $project: {
        _id: 1,
        score: { $meta: "vectorSearchScore" },
        metadata: 1,
      },
    } as any;

    const pipeline = [
      vectorStage,
      ...(matchClause ? [{ $match: matchClause }] : []),
      projectStage,
    ];
    const cursor = this.collection(indexName).aggregate(pipeline);
    const results = await cursor.toArray();
    return results.map((r: any) => ({ id: r._id, score: r.score as number, metadata: r.metadata }));
  }

  async deleteNamespace(indexName: string, namespace: string) {
    await this.collection(indexName).deleteMany({ namespace });
  }

  async getStats(indexName: string) {
    const pipeline = [
      { $group: { _id: "$namespace", count: { $sum: 1 } } },
    ];
    const stats = await this.collection(indexName).aggregate(pipeline).toArray();
    const namespaces: Record<string, { recordCount: number }> = {};
    for (const s of stats) {
      const key = s._id || "";
      namespaces[key] = { recordCount: s.count };
    }
    const total = await this.collection(indexName).countDocuments();
    return { namespaces, totalRecordCount: total };
  }

  private buildFilter(filter: Record<string, any> | undefined, namespace?: string) {
    const clauses: any[] = [];
    if (namespace) clauses.push({ namespace });
    if (filter && Object.keys(filter).length > 0) {
      // Translate Pinecone-style operators {$eq: value} into Mongo
      const translated: any = {};
      for (const [k, v] of Object.entries(filter)) {
        if (v && typeof v === "object" && (v as any).$eq !== undefined) {
          translated[`metadata.${k}`] = (v as any).$eq;
        } else {
          translated[`metadata.${k}`] = v;
        }
      }
      clauses.push(translated);
    }
    if (clauses.length === 0) return undefined;
    if (clauses.length === 1) return clauses[0];
    return { $and: clauses };
  }
}
