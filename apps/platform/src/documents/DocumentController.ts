/**
 * @swagger
 * tags:
 *   name: Document
 *   description: Document management and analysis endpoints
 */

import Router from "@koa/router";
import { locationRoleMiddleware } from "../locations/LocationService";
import parse from "../storage/FileStream";
import {
  uploadDocument,
  getDocumentWithAnalysis,
  listDocuments,
  getDocumentDownloadUrl,
  deleteDocument,
  reprocessDocument,
} from "./DocumentService";
import { DocumentVectorService } from "./DocumentVectorService";

const router = new Router({
  prefix: "/documents",
});

/**
 * @swagger
 * /documents/upload:
 *   post:
 *     summary: Upload Document
 *     description: Uploads a document for processing and analysis
 *     tags: [Document]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *     responses:
 *       200:
 *         description: Document uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 document_id:
 *                   type: string
 *                 status:
 *                   type: string
 *       400:
 *         description: Invalid file or upload failed
 */
router.post("/upload", locationRoleMiddleware("editor"), async (ctx) => {
  try {
    const stream = await parse(ctx);
    const location_id = ctx.state.location.id;
    const user_id = ctx.state.user?.id;

    // Free-tier AI usage enforcement for document upload/analysis
    try {
      const AIUsageLimiter = (await import("../usage/AIUsageLimiter")).default;
      const adminEmail = ctx.state.admin
        ? (await (await import("../auth/AdminRepository")).getAdmin(
            ctx.state.admin.id,
            ctx.state.location.organization_id
          ))?.email
        : undefined;
      await AIUsageLimiter.checkAndConsume({
        location_id: ctx.state.location.id,
        organization_id: ctx.state.location.organization_id,
        admin_email: adminEmail,
      });
    } catch (err: any) {
      ctx.status = err.status || 402;
      ctx.body = { error: err.message || "AI usage limit reached", code: "AI_USAGE_LIMIT_REACHED" };
      return;
    }

    console.log("uploading document", location_id, stream, user_id);
    // Use the document service for upload handling
    const result = await uploadDocument(location_id, stream, user_id);

    // Return immediate response with document info
    ctx.status = 200;
    ctx.body = {
      document_id: result.document.id,
      status: "processing",
    };
  } catch (error) {
    ctx.status = 400;
    ctx.body = {
      error: "Invalid file or upload failed",
      details: error instanceof Error ? error.message : "Unknown error",
    };
  }
});

/**
 * @swagger
 * /documents/{document_id}/analysis:
 *   get:
 *     summary: Get Document Analysis
 *     description: Retrieves the analysis results for a specific document
 *     tags: [Document]
 *     parameters:
 *       - in: path
 *         name: document_id
 *         required: true
 *         schema:
 *           type: string
 *         description: Document identifier
 *     responses:
 *       200:
 *         description: Document analysis results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 analysis:
 *                   type: object
 *       404:
 *         description: Document not found
 */
router.get(
  "/:document_id(\\d+)/analysis",
  locationRoleMiddleware("editor"),
  async (ctx) => {
    const document_id = parseInt(ctx.params.document_id);
    const location_id = ctx.state.location.id;

    try {
      const result = await getDocumentWithAnalysis(document_id, location_id);
      console.log({ result });
      ctx.body = result;
    } catch (error) {
      ctx.status =
        error instanceof Error && error.message.includes("not found")
          ? 404
          : 400;
      ctx.body = {
        error:
          error instanceof Error
            ? error.message
            : "Failed to retrieve document analysis",
      };
    }
  }
);

/**
 * @swagger
 * /documents:
 *   get:
 *     summary: List Documents
 *     description: Retrieves a list of documents with optional filtering
 *     tags: [Document]
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: Filter by document status
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of documents
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 documents:
 *                   type: array
 *                   items:
 *                     type: object
 *                 total:
 *                   type: integer
 *                 page:
 *                   type: integer
 *                 limit:
 *                   type: integer
 */
router.get("/", locationRoleMiddleware("editor"), async (ctx) => {
  const location_id = ctx.state.location.id;

  try {
    const documents = await listDocuments(location_id);
    ctx.body = {
      documents,
      total: documents.length,
      page: 1,
      limit: documents.length,
    };
  } catch (error) {
    ctx.status = 500;
    ctx.body = {
      error: "Failed to retrieve documents",
      details: error instanceof Error ? error.message : "Unknown error",
    };
  }
});

/**
 * @swagger
 * /documents/{document_id}/download:
 *   get:
 *     summary: Download Document
 *     description: Downloads a specific document
 *     tags: [Document]
 *     parameters:
 *       - in: path
 *         name: document_id
 *         required: true
 *         schema:
 *           type: string
 *         description: Document identifier
 *     responses:
 *       200:
 *         description: Document file
 *         content:
 *           application/octet-stream:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: Document not found
 */
router.get(
  "/:document_id/download",
  locationRoleMiddleware("editor"),
  async (ctx) => {
    const document_id = parseInt(ctx.params.document_id);
    const location_id = ctx.state.location.id;

    try {
      const downloadUrl = await getDocumentDownloadUrl(
        document_id,
        location_id
      );

      // Optional page deep-link support (e.g., PDFs): if page is provided, append as fragment
      const page = ctx.query.page as string | undefined;
      const finalUrl = page ? `${downloadUrl}#page=${encodeURIComponent(page)}` : downloadUrl;

      ctx.redirect(finalUrl);
    } catch (error) {
      ctx.status =
        error instanceof Error && error.message.includes("not found")
          ? 404
          : 500;
      ctx.body = {
        error: "Failed to download document",
        details: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }
);

/**
 * @swagger
 * /documents/{document_id}:
 *   delete:
 *     summary: Delete Document
 *     description: Deletes a specific document
 *     tags: [Document]
 *     parameters:
 *       - in: path
 *         name: document_id
 *         required: true
 *         schema:
 *           type: string
 *         description: Document identifier
 *     responses:
 *       204:
 *         description: Document deleted successfully
 *       404:
 *         description: Document not found
 */
router.delete(
  "/:document_id",
  locationRoleMiddleware("editor"),
  async (ctx) => {
    const document_id = parseInt(ctx.params.document_id);
    const location_id = ctx.state.location.id;

    try {
      const result = await deleteDocument(document_id, location_id);
      ctx.status = 204;
    } catch (error) {
      ctx.status =
        error instanceof Error && error.message.includes("not found")
          ? 404
          : 500;
      ctx.body = {
        error: "Failed to delete document",
        details: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }
);

/**
 * @swagger
 * /documents/{document_id}/reprocess:
 *   post:
 *     summary: Reprocess Document
 *     description: Triggers reprocessing of a document
 *     tags: [Document]
 *     parameters:
 *       - in: path
 *         name: document_id
 *         required: true
 *         schema:
 *           type: string
 *         description: Document identifier
 *     responses:
 *       200:
 *         description: Document reprocessing started
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *       404:
 *         description: Document not found
 */
router.post(
  "/:document_id/reprocess",
  locationRoleMiddleware("editor"),
  async (ctx) => {
    const document_id = parseInt(ctx.params.document_id);
    const location_id = ctx.state.location.id;

    try {
      // Free-tier AI usage enforcement for reprocessing (re-analysis)
      try {
        const AIUsageLimiter = (await import("../usage/AIUsageLimiter")).default;
        await AIUsageLimiter.checkAndConsume({
          location_id: ctx.state.location.id,
          organization_id: ctx.state.location.organization_id,
        });
      } catch (err: any) {
        ctx.status = err.status || 402;
        ctx.body = { error: err.message || "AI usage limit reached", code: "AI_USAGE_LIMIT_REACHED" };
        return;
      }

      const result = await reprocessDocument(document_id, location_id);
      ctx.status = 200;
      ctx.body = {
        status: "processing",
      };
    } catch (error) {
      ctx.status =
        error instanceof Error && error.message.includes("not found")
          ? 404
          : 500;
      ctx.body = {
        error: "Failed to reprocess document",
        details: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }
);

/**
 * @swagger
 * /documents/vectorization-status:
 *   get:
 *     summary: Get Vectorization Status
 *     description: Get the current vectorization status for all documents in a location
 *     tags: [Document]
 *     responses:
 *       200:
 *         description: Vectorization status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   enum: [completed, in_progress, failed, not_started]
 *                 indexedCount:
 *                   type: integer
 *                 inProgressCount:
 *                   type: integer
 *                 failedCount:
 *                   type: integer
 *                 notStartedCount:
 *                   type: integer
 *       500:
 *         description: Failed to get vectorization status
 */
router.get("/vectorization-status", locationRoleMiddleware("editor"), async (ctx) => {
  const location_id = ctx.state.location.id;

  try {
    const status = await DocumentVectorService.getVectorizationStatus(location_id);
    ctx.body = status;
  } catch (error) {
    ctx.status = 500;
    ctx.body = {
      error: "Failed to get vectorization status",
      details: error instanceof Error ? error.message : "Unknown error",
    };
  }
});

/**
 * @swagger
 * /admin/locations/{location_id}/documents/clear-stuck-jobs:
 *   post:
 *     summary: Clear stuck document processing jobs
 *     description: Manually clear document processing jobs that are stuck in processing state
 *     tags: [Document]
 *     parameters:
 *       - in: path
 *         name: location_id
 *         required: true
 *         schema:
 *           type: string
 *         description: Location ID
 *     responses:
 *       200:
 *         description: Successfully cleared stuck jobs
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 clearedCount:
 *                   type: integer
 *       500:
 *         description: Failed to clear stuck jobs
 */
router.post("/clear-stuck-jobs", locationRoleMiddleware("editor"), async (ctx) => {
  const location_id = ctx.state.location.id;

  try {
    const { OnboardingJobTracker } = await import("../locations/OnboardingJobTracker");
    const clearedCount = OnboardingJobTracker.cleanupStuckJobs(location_id, 0); // Clear all processing jobs immediately

    ctx.body = {
      message: `Cleared ${clearedCount} stuck document processing jobs`,
      clearedCount,
    };
  } catch (error) {
    ctx.status = 500;
    ctx.body = {
      error: "Failed to clear stuck jobs",
      details: error instanceof Error ? error.message : "Unknown error",
    };
  }
});

export default router;
