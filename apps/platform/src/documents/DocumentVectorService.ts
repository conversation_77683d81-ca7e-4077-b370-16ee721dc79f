import { Document } from "./Document";
import { VectorService, VectorData } from "../core/VectorService";
import { logger } from "../config/logger";
import Storage from "../storage/Storage";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";
import { FileStream } from "../storage/FileStream";
import mammoth from "mammoth";
import pdfParse from "pdf-parse";
import csv from "csv-parser";
import xlsx from "xlsx";
import FormData from "form-data";
import fetch from "node-fetch";
import crypto from "crypto";

import {
  OnboardingJobTracker,
  OnboardingJob,
} from "../locations/OnboardingJobTracker";
import DocumentVectorJob from "./DocumentVectorJob";

// Constants for the service
const DOCUMENT_INDEX = "document-embeddings";
const CHUNK_SIZE = 1000; // Characters per chunk
const CHUNK_OVERLAP = 200; // Characters of overlap between chunks
const MAX_BATCH_SIZE = 2; // 🛡️ CONSERVATIVE: Process 2 chunks at a time to prevent OOM
const PROCESSING_DELAY = 200; // 🛡️ CONSERVATIVE: 200ms delay between chunks for memory recovery

// Python microservice configuration
const PYTHON_SERVICE_URL = process.env.PYTHON_SERVICE_URL || "http://document-processor:8000";
const USE_PYTHON_SERVICE = process.env.USE_PYTHON_SERVICE === "true" || true; // Default to true
const PYTHON_SERVICE_POLL_INTERVAL_SECONDS = parseInt(process.env.PYTHON_SERVICE_POLL_INTERVAL_SECONDS || "10"); // Default 10 seconds for responsive polling

// Log configuration on startup
logger.info(`📡 Python service configuration:`, {
  PYTHON_SERVICE_URL,
  USE_PYTHON_SERVICE,
  PYTHON_SERVICE_POLL_INTERVAL_SECONDS,
  env_PYTHON_SERVICE_URL: process.env.PYTHON_SERVICE_URL,
  env_USE_PYTHON_SERVICE: process.env.USE_PYTHON_SERVICE
});

export interface DocumentChunk {
  id: string;
  content: string;
  // Optional similarity score from vector search (higher is better). May be undefined when using text fallback
  score?: number;
  metadata: {
    document_id: number;
    location_id: number;
    chunk_index: number;
    total_chunks: number;
    page?: number;
    document_name: string;
    document_type: string;
    created_at: number;
  };
}

export class DocumentVectorService {
  private static vectorService: VectorService | null = null;

  /**
   * Get the vector service instance
   */
  private static getVectorService(): VectorService {
    if (!this.vectorService) {
      this.vectorService = VectorService.getInstance();
    }
    return this.vectorService;
  }

  /**
   * Initialize the vector service and ensure the document index exists
   */
  static async initialize(): Promise<boolean> {
    try {
      await this.getVectorService().initialize();
      await this.ensureIndexExists();
      logger.info("DocumentVectorService initialized successfully");
      return true;
    } catch (error) {
      logger.error("Failed to initialize DocumentVectorService:", error);
      throw error;
    }
  }

  /**
   * Processes a document and adds it to the vector database
   */
  static async processDocument(
    documentId: number,
    locationId: number,
    file?: FileStream
  ): Promise<boolean> {
    try {
      logger.info(`🚀🚀🚀 DOCUMENT PROCESSING STARTED - DocumentVectorService.processDocument called:`, {
        documentId,
        locationId,
        hasFile: !!file,
        PYTHON_SERVICE_URL,
        USE_PYTHON_SERVICE
      });

      // Get the document record from the database
      const document = await Document.first((qb) =>
        qb.where({ id: documentId, location_id: locationId })
      );

      if (!document) {
        throw new Error(
          `Document ${documentId} not found for location ${locationId}`
        );
      }

      // Use Python microservice for processing if enabled
      if (USE_PYTHON_SERVICE) {
        return await this.processDocumentWithPythonService(
          documentId,
          locationId,
          document,
          file
        );
      }

      // Fallback to original Node.js processing
      return await this.processDocumentWithNodeJS(
        documentId,
        locationId,
        document,
        file
      );
    } catch (error) {
      logger.error(`Error processing document ${documentId}:`, error);

      // Update document with error status (merge to preserve existing analysis)
      try {
        const currentDocument = await Document.first((qb) => qb.where({ id: documentId }));
        let existingData: any = {};
        if (currentDocument && currentDocument.data) {
          if (typeof currentDocument.data === "string") {
            try { existingData = JSON.parse(currentDocument.data); } catch {}
          } else {
            existingData = currentDocument.data;
          }
        }

        await Document.update((qb) => qb.where({ id: documentId }), {
          data: {
            ...existingData,
            vectorization_error: error instanceof Error ? error.message : String(error),
            vectorization_error_time: new Date().toISOString(),
          },
        });
      } catch (updateError) {
        logger.error(`Failed to update document ${documentId} with error status:`, updateError);
      }

      return false;
    }
  }

  /**
   * Process document using Python microservice
   */
  private static async processDocumentWithPythonService(
    documentId: number,
    locationId: number,
    document: any,
    file?: FileStream
  ): Promise<boolean> {
    let tempFilePath: string | null = null;

    try {
      logger.info(`🚀🚀🚀 PYTHON SERVICE METHOD CALLED - Using Python microservice for document ${documentId}`);
      logger.info(`🚀🚀🚀 PYTHON SERVICE URL: ${PYTHON_SERVICE_URL}`);
      logger.info(`🚀🚀🚀 File parameter:`, file ? 'provided' : 'null');
      logger.info(`🚀🚀🚀 Document storage_path:`, document.storage_path || 'not set');

      // Prepare file for upload to Python service
      if (file) {
        // Convert FileStream to temporary file
        const chunks: Buffer[] = [];
        for await (const chunk of file.file as any) {
          chunks.push(Buffer.from(chunk));
        }
        const contentBuffer = Buffer.concat(chunks);
        chunks.length = 0; // Clear chunks array

        const tempDir = path.join(os.tmpdir(), "document-processor");
        fs.mkdirSync(tempDir, { recursive: true });
        tempFilePath = path.join(tempDir, file.metadata.fileName);
        fs.writeFileSync(tempFilePath, contentBuffer);
      } else if (document.storage_path) {
        // Resolve and download the file to a local temp path before posting to Python
        const tempDir = path.join(os.tmpdir(), "document-processor");
        fs.mkdirSync(tempDir, { recursive: true });
        tempFilePath = path.join(tempDir, document.name || `doc_${documentId}`);

        // Build absolute URL to fetch from storage
        let publicUrl = Storage.url(document.storage_path);
        if (publicUrl.startsWith('/')) {
          const baseUrl = process.env.BASE_URL || process.env.STORAGE_BASE_URL || 'http://api:3001';
          publicUrl = `${baseUrl}${publicUrl}`;
        }
        if (!publicUrl.startsWith('http://') && !publicUrl.startsWith('https://')) {
          throw new Error(`Invalid storage URL: ${publicUrl}`);
        }
        logger.info(`🌐 Downloading document for Python upload from: ${publicUrl}`);
        const downloadRes = await fetch(publicUrl);
        if (!downloadRes.ok) {
          throw new Error(`Failed to download file for Python upload: ${downloadRes.status} ${downloadRes.statusText}`);
        }
        const arrayBuffer = await downloadRes.arrayBuffer();
        fs.writeFileSync(tempFilePath, Buffer.from(arrayBuffer));
        logger.info(`📄 Temp file prepared for Python upload at: ${tempFilePath}`);
      } else {
        throw new Error("No file provided for processing - neither file parameter nor storage_path available");
      }

      if (!tempFilePath) {
        throw new Error("Failed to prepare file for processing");
      }

      // Before sending to Python, clean previous vectors for this document to avoid duplicates/stale chunks
      try {
        const namespace = this.getNamespace(locationId);
        await this.getVectorService().deleteVectors(
          DOCUMENT_INDEX,
          { source_type: "document", source_id: documentId.toString() },
          namespace
        );
        logger.info(`Deleted existing vectors for document ${documentId} in ${namespace} (pre-Python)`);
      } catch (delErr) {
        logger.warn(`Failed to delete existing vectors for document ${documentId} before Python processing: ${delErr instanceof Error ? delErr.message : String(delErr)}`);
      }

      // Create form data for Python service
      const formData = new FormData();
      formData.append('document_id', documentId.toString());
      formData.append('location_id', locationId.toString());
      formData.append('file', fs.createReadStream(tempFilePath));

      // Test Python service connectivity first
      const healthUrl = `${PYTHON_SERVICE_URL}/health`;
      logger.info(`🔍 Testing Python service connectivity at: ${healthUrl}`);
      try {
        const healthResponse = await fetch(healthUrl, { timeout: 10000 }); // 10 second timeout
        logger.info(`🔍 Python service health check: ${healthResponse.status} ${healthResponse.statusText}`);
      } catch (healthError) {
        logger.error(`🔍 Python service health check failed:`, healthError);
        throw new Error(`Python service unreachable: ${healthError instanceof Error ? healthError.message : String(healthError)}`);
      }

      // Send request to Python service
      const pythonUrl = `${PYTHON_SERVICE_URL}/api/v1/process-document`;
      logger.info(`🐍 Calling Python service at: ${pythonUrl}`);
      logger.info(`📦 FormData contents:`, {
        document_id: documentId,
        location_id: locationId,
        has_file: !!tempFilePath,
        temp_file_path: tempFilePath
      });

      const startTime = Date.now();
      logger.info(`⏱️ Starting Python service request at ${new Date().toISOString()}`);

      // Set proper multipart headers for form-data package
      const headers: any = (formData as any).getHeaders ? (formData as any).getHeaders() : {};

      // Robust timeout using AbortController (works with node-fetch v2/v3 and global fetch)
      const controller = new AbortController();
      const timeoutMs = 900000; // 15 minutes
      const timeoutHandle = setTimeout(() => controller.abort(), timeoutMs);

      let response: any;
      try {
        response = await fetch(pythonUrl, {
          method: 'POST',
          body: formData as any,
          headers,
          // @ts-ignore - fetch implementations accept AbortSignal
          signal: controller.signal,
        });
      } finally {
        clearTimeout(timeoutHandle);
      }

      const requestDuration = Date.now() - startTime;
      logger.info(`⏱️ Python service request completed in ${requestDuration}ms (${(requestDuration / 1000).toFixed(1)}s)`);

      logger.info(`📡 Python service response:`, {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      });

      if (!response.ok) {
        const errorText = await response.text();
        logger.error(`❌ Python service error response:`, {
          status: response.status,
          statusText: response.statusText,
          errorText
        });
        throw new Error(`Python service error: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      logger.info(`Python service response:`, result);

      // Poll for completion
      const statusUrl = `${PYTHON_SERVICE_URL}/api/v1/status/${documentId}`;
      logger.info(`🔍🔍🔍 STATUS URL DEBUG:`, {
        PYTHON_SERVICE_URL,
        statusUrl,
        documentId,
        env_PYTHON_SERVICE_URL: process.env.PYTHON_SERVICE_URL
      });
      let attempts = 0;
      const pollIntervalMs = PYTHON_SERVICE_POLL_INTERVAL_SECONDS * 1000;
      const maxAttempts = Math.ceil((15 * 60 * 1000) / pollIntervalMs); // 15 minutes total, divided by poll interval
      const pollingStartTime = Date.now();

      logger.info(`🔍 Starting status polling with ${PYTHON_SERVICE_POLL_INTERVAL_SECONDS}s intervals (max ${maxAttempts} attempts)`);

      while (attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, pollIntervalMs));

        logger.info(`🔍🔍🔍 ATTEMPTING STATUS CHECK:`, { statusUrl, attempt: attempts + 1 });
        const statusResponse = await fetch(statusUrl);
        if (!statusResponse.ok) {
          throw new Error(`Failed to check status: ${statusResponse.status}`);
        }

        const status = await statusResponse.json();
        logger.info(`Processing status for ${documentId}:`, status.status);

        if (status.status === 'completed') {
          // Update document with success status - PRESERVE existing data (e.g., analysis)
          const currentDocument = await Document.first((qb) => qb.where({ id: documentId }));
          let existingData: any = {};
          if (currentDocument && currentDocument.data) {
            if (typeof currentDocument.data === "string") {
              try { existingData = JSON.parse(currentDocument.data); } catch {}
            } else {
              existingData = currentDocument.data;
            }
          }

          await Document.update((qb) => qb.where({ id: documentId }), {
            pinecone_file_id: `python_${documentId}`,
            data: {
              ...existingData,
              vectorization: {
                status: "completed",
                chunks_total: status.total_chunks || 0,
                chunks_indexed: status.processed_chunks || 0,
                chunks_failed: 0,
                completed_at: new Date().toISOString(),
              },
            },
          });

          return true;
        } else if (status.status === 'failed') {
          throw new Error(`Python service processing failed: ${status.error_message}`);
        }

        attempts++;
      }

      const totalPollingTime = Date.now() - pollingStartTime;
      throw new Error(`Processing timeout - Python service did not complete within ${(totalPollingTime / 1000 / 60).toFixed(1)} minutes (${attempts} attempts)`);
    } catch (error) {
      logger.error(`🔥🔥🔥 PYTHON SERVICE ERROR - processing failed for document ${documentId}:`, error);
      logger.error(`🔥🔥🔥 DETAILED ERROR for document ${documentId}:`, {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        documentId,
        locationId,
        PYTHON_SERVICE_URL
      });
      throw error;
    } finally {
      // Cleanup temporary file if we created one
      if (tempFilePath && file && fs.existsSync(tempFilePath)) {
        try {
          fs.unlinkSync(tempFilePath);
          logger.debug(`Cleaned up temporary file: ${tempFilePath}`);
        } catch (cleanupError) {
          logger.warn(`Failed to cleanup temporary file ${tempFilePath}:`, cleanupError);
        }
      }
    }
  }

  /**
   * Original Node.js processing method (fallback)
   */
  private static async processDocumentWithNodeJS(
    documentId: number,
    locationId: number,
    document: any,
    file?: FileStream
  ): Promise<boolean> {
    try {
      logger.info(`Using Node.js processing for document ${documentId}`);

      // 🚀 PYTHON SERVICE: Allow up to 10MB files since we're using Python microservice
      const MAX_FILE_SIZE_MB = 10; // Match Python document processor limit
      if (document.size > MAX_FILE_SIZE_MB * 1024 * 1024) {
        const fileSizeMB = (document.size / (1024 * 1024)).toFixed(1);
        logger.warn(
          `Document ${documentId} is too large (${fileSizeMB}MB) for processing. Skipping vectorization.`
        );

        // Update document status to failed with shorter error message
        await Document.update(
          (qb) => qb.where({ id: documentId }),
          {
            status: "failed",
            data: {
              ...document.data,
              vectorization_error: `File too large: ${fileSizeMB}MB > ${MAX_FILE_SIZE_MB}MB`,
              vectorization_error_time: new Date().toISOString(),
            },
          }
        );

        throw new Error(`Document ${documentId} is too large (${fileSizeMB}MB) for processing`);
      }

      // Download the document file if not provided
      let tempFilePath: string | null = null;

      if (!file && document.storage_path) {
        try {
          logger.info(
            `Downloading document from storage: ${document.storage_path}`
          );

          // Log storage configuration for debugging
          logger.info(`🔧 Storage configuration:`, {
            storage_driver: process.env.STORAGE_DRIVER,
            storage_base_url: process.env.STORAGE_BASE_URL,
            base_url: process.env.BASE_URL,
            node_env: process.env.NODE_ENV
          });

          // Get the public URL for the file
          let publicUrl = Storage.url(document.storage_path);

          // Ensure absolute URL for fetch() - fix "Only absolute URLs are supported" error
          if (publicUrl.startsWith('/')) {
            const baseUrl = process.env.BASE_URL || process.env.STORAGE_BASE_URL || 'https://bakedbot-nnwe.onrender.com';
            publicUrl = `${baseUrl}${publicUrl}`;
          }

          logger.info(`📁 Storage details:`, {
            storage_path: document.storage_path,
            storage_url_raw: Storage.url(document.storage_path),
            public_url: publicUrl,
            document_id: document.id,
            document_name: document.name,
            base_url: process.env.BASE_URL,
            storage_base_url: process.env.STORAGE_BASE_URL
          });

          // Validate URL before fetch
          if (!publicUrl.startsWith('http://') && !publicUrl.startsWith('https://')) {
            throw new Error(`Invalid URL generated: ${publicUrl}. Expected absolute URL starting with http:// or https://`);
          }

          // Fetch the file content
          logger.info(`🌐 Fetching document from: ${publicUrl}`);
          const response = await fetch(publicUrl);
          logger.info(`📡 Storage fetch response:`, {
            status: response.status,
            statusText: response.statusText,
            url: publicUrl
          });

          if (!response.ok) {
            // Try direct download from storage provider as fallback
            logger.warn(`URL download failed (${response.status}), trying direct storage download...`);
            try {
              const storage = new Storage();
              const contentBuffer = await storage.download(document.storage_path);
              logger.info(`✅ Direct storage download successful`);

              // Create a temporary file to work with
              const tempDir = path.join(os.tmpdir(), "document-processor");
              fs.mkdirSync(tempDir, { recursive: true });
              tempFilePath = path.join(tempDir, document.name);
              fs.writeFileSync(tempFilePath, contentBuffer);
            } catch (directDownloadError) {
              logger.error(`Direct storage download also failed:`, directDownloadError);
              throw new Error(
                `Failed to download file from storage: ${response.statusText} (${response.status}) - URL: ${publicUrl}. Direct download also failed: ${directDownloadError instanceof Error ? directDownloadError.message : String(directDownloadError)}`
              );
            }
          } else {
            const arrayBuffer = await response.arrayBuffer();
            const contentBuffer = Buffer.from(arrayBuffer);

            // Create a temporary file to work with
            const tempDir = path.join(os.tmpdir(), "document-processor");
            fs.mkdirSync(tempDir, { recursive: true });
            tempFilePath = path.join(tempDir, document.name);
            fs.writeFileSync(tempFilePath, contentBuffer);
          }

          // 🧹 IMMEDIATE CLEANUP: Force garbage collection after writing to disk
          if (global.gc) {
            global.gc();
            logger.debug(`Forced garbage collection after file download for document ${documentId}`);
          }

          logger.info(
            `Document ${documentId} downloaded to temporary file: ${tempFilePath}`
          );
        } catch (downloadError) {
          logger.error(
            `Error downloading document ${documentId}:`,
            downloadError
          );
          throw new Error(
            `Failed to download document: ${
              downloadError instanceof Error
                ? downloadError.message
                : String(downloadError)
            }`
          );
        }
      } else if (file) {
        try {
          // Convert FileStream to buffer and temporary file
          const chunks: Buffer[] = [];
          for await (const chunk of file.file as any) {
            chunks.push(Buffer.from(chunk));
          }
          const contentBuffer = Buffer.concat(chunks);

          // Clear chunks array immediately to free memory
          chunks.length = 0;

          // Create a temporary file
          const tempDir = path.join(os.tmpdir(), "document-processor");
          fs.mkdirSync(tempDir, { recursive: true });
          tempFilePath = path.join(tempDir, file.metadata.fileName);
          fs.writeFileSync(tempFilePath, contentBuffer);

          // 🧹 IMMEDIATE CLEANUP: Force garbage collection after writing to disk
          if (global.gc) {
            global.gc();
            logger.debug(`Forced garbage collection after file stream processing for document ${documentId}`);
          }

          logger.info(
            `Document ${documentId} from stream saved to temporary file: ${tempFilePath}`
          );
        } catch (fileError) {
          logger.error(
            `Error processing file stream for document ${documentId}:`,
            fileError
          );
          throw new Error(
            `Failed to process file stream: ${
              fileError instanceof Error ? fileError.message : String(fileError)
            }`
          );
        }
      } else {
        throw new Error(
          `No file or storage path available for document ${documentId}`
        );
      }

      // Extract content based on file type
      const fileName = document.name;
      const fileType = document.type || this.getMimeTypeFromFileName(fileName);

      logger.info(`Extracting content from ${fileName} (type: ${fileType})`);

      let extractedContent: string;
      try {
        extractedContent = await this.extractContent(
          tempFilePath!,
          fileName,
          fileType
        );
        // Compute a stable document hash to support de-dup and change detection
        const documentHash = crypto.createHash("sha256").update(extractedContent).digest("hex");
        // Early exit if nothing changed since last vectorization
        try {
          const currentDocument = await Document.first((qb) => qb.where({ id: documentId }));
          const dataObj = currentDocument?.data
            ? (typeof currentDocument.data === "string"
                ? JSON.parse(currentDocument.data)
                : currentDocument.data)
            : {};
          const prevHash = dataObj?.vectorization?.document_hash;
          if (prevHash && prevHash === documentHash) {
            logger.info(`No content change detected for document ${documentId} (hash match). Skipping re-vectorization.`);
            return true;
          }
        } catch (hashCheckErr) {
          logger.warn(`Failed to check previous document hash for ${documentId}: ${hashCheckErr instanceof Error ? hashCheckErr.message : String(hashCheckErr)}`);
        }
      } catch (extractError) {
        logger.error(
          `Error extracting content from ${fileName}:`,
          extractError
        );
        throw new Error(
          `Content extraction failed: ${
            extractError instanceof Error
              ? extractError.message
              : String(extractError)
          }`
        );
      }

      // Clean up temporary file and force garbage collection
      if (tempFilePath) {
        try {
          fs.unlinkSync(tempFilePath);
          logger.debug(`Deleted temporary file: ${tempFilePath}`);
        } catch (unlinkError) {
          logger.warn(
            `Failed to delete temporary file ${tempFilePath}:`,
            unlinkError
          );
        }
      }

      // Force garbage collection after content extraction
      if (global.gc) {
        global.gc();
        logger.debug(`Forced garbage collection after content extraction`);
      }

      // 🛡️ CONSERVATIVE: Limit chunks to prevent OOM
      const maxReasonableChunks = 20; // 🛡️ CONSERVATIVE: 20 chunks max (~20KB of text content) for memory safety
      const estimatedChunks = Math.ceil(extractedContent.length / CHUNK_SIZE);

      if (estimatedChunks > maxReasonableChunks) {
        logger.warn(
          `Document ${documentId} is very large (${estimatedChunks} estimated chunks). ` +
            `Truncating to first ${
              maxReasonableChunks * CHUNK_SIZE
            } characters to prevent memory issues.`
        );
        extractedContent = extractedContent.substring(
          0,
          maxReasonableChunks * CHUNK_SIZE
        );
      }

      // Create chunks from the extracted content
      const chunks = this.createChunks(
        extractedContent,
        documentId,
        locationId,
        document.name,
        document.type
      );

      if (chunks.length === 0) {
        logger.warn(`No content chunks created for document ${documentId}`);

        // Update document with completed status (no chunks to vectorize)
        const currentDocument = await Document.first((qb) =>
          qb.where({ id: documentId })
        );

        // Parse existing data if it's a string
        let existingData = {};
        if (currentDocument?.data) {
          if (typeof currentDocument.data === "string") {
            try {
              existingData = JSON.parse(currentDocument.data);
            } catch (e) {
              logger.warn(
                `Failed to parse existing document data for ${documentId}: ${e}`
              );
              existingData = {};
            }
          } else {
            existingData = currentDocument.data;
          }
        }

        await Document.update((qb) => qb.where({ id: documentId }), {
          pinecone_file_id: `internal_${documentId}`,
          data: {
            ...existingData,
            vectorization: {
              status: "completed",
              chunks_total: 0,
              chunks_indexed: 0,
              chunks_failed: 0,
              completed_at: new Date().toISOString(),
            },
          },
        });

        logger.info(
          `Document ${documentId} vectorization completed with no chunks (likely scanned document)`
        );

        // Also log completion info for job tracking
        logger.info(
          `Completing document_vectorization job for location ${locationId}`
        );
        return true; // Consider this a successful completion
      }

      logger.info(
        `Created ${chunks.length} content chunks for document ${documentId}`
      );

      // Use namespace pattern for location-specific data
      const namespace = this.getNamespace(locationId);

      // Clean previous vectors for this document to avoid duplicates/stale chunks
      try {
        await this.getVectorService().deleteVectors(
          DOCUMENT_INDEX,
          { source_type: "document", source_id: documentId.toString() },
          namespace
        );
        logger.info(`Deleted existing vectors for document ${documentId} in ${namespace}`);
      } catch (delErr) {
        logger.warn(`Failed to delete existing vectors for document ${documentId}: ${delErr instanceof Error ? delErr.message : String(delErr)}`);
      }

      // 🚀 FRIENDLY STREAMING VECTORIZATION - Process chunks in small batches
      logger.info(
        `Starting friendly streaming vectorization for ${chunks.length} chunks`
      );

      let processedCount = 0;
      let totalVectorsUpserted = 0;
      let totalErrorCount = 0;

      // Process chunks in small batches to prevent memory overload
      for (let i = 0; i < chunks.length; i += MAX_BATCH_SIZE) {
        const batchChunks = chunks.slice(i, i + MAX_BATCH_SIZE);

        logger.info(
          `Processing batch ${Math.ceil(
            (i + 1) / MAX_BATCH_SIZE
          )} of ${Math.ceil(chunks.length / MAX_BATCH_SIZE)} (${
            batchChunks.length
          } chunks)`
        );

        // Prepare vector data for this batch
        const batchVectorData = batchChunks.map(
          (chunk): VectorData => ({
            // Use deterministic ID; include content hash portion to avoid duplicates across re-chunking patterns
            id: chunk.id,
            text: chunk.content,
            metadata: {
              ...chunk.metadata,
              document_id: documentId,
              location_id: locationId,
              source_type: "document",
              source_id: documentId.toString(),
              content: chunk.content,
              content_hash: crypto.createHash("sha256").update(chunk.content).digest("hex"),
              created_at: Date.now(),
              updated_at: Date.now(),
            },
          })
        );

        try {
          // Process this batch with smaller size for memory efficiency
          const batchResult = await this.getVectorService().upsertVectors(
            DOCUMENT_INDEX,
            batchVectorData,
            batchChunks.length, // Use actual batch size
            namespace
          );

          totalVectorsUpserted +=
            batchResult.successCount || batchChunks.length;
          totalErrorCount += batchResult.errorCount || 0;
          processedCount += batchChunks.length;

          logger.info(
            `Batch completed: ${processedCount}/${chunks.length} chunks processed`
          );

          // 🚨 EMERGENCY: Aggressive memory cleanup after every chunk
          if (global.gc) {
            global.gc();
            logger.debug(
              `Emergency memory cleanup performed after chunk ${processedCount}`
            );
          }

          // 🚨 EMERGENCY: Longer delay for memory recovery
          await new Promise((resolve) => setTimeout(resolve, PROCESSING_DELAY));

          // 🚨 EMERGENCY: Additional memory cleanup
          batchVectorData.length = 0; // Clear the array
          if (global.gc) {
            global.gc();
          }
        } catch (error) {
          logger.error(
            `Error processing batch ${Math.ceil(
              (i + 1) / MAX_BATCH_SIZE
            )}: ${error}`
          );
          throw error;
        }
      }

      logger.info(
        `🎉 Friendly vectorization completed! Processed ${processedCount} chunks, upserted ${totalVectorsUpserted} vectors`
      );

      const result = {
        successCount: totalVectorsUpserted,
        errorCount: totalErrorCount,
        vectorsUpserted: totalVectorsUpserted,
      };

      logger.info(
        `Document ${documentId} indexing complete: ${totalVectorsUpserted} vectors indexed successfully`
      );

      // Force garbage collection after vectorization
      if (global.gc) {
        global.gc();
        logger.debug(`Forced garbage collection after vectorization`);
      }

      // Update document record with vectorization status
      // First, ensure we get the latest document data
      const currentDocument = await Document.first((qb) =>
        qb.where({ id: documentId })
      );

      // Parse existing data if it's a string
      let existingData = {};
      if (currentDocument?.data) {
        if (typeof currentDocument.data === "string") {
          try {
            existingData = JSON.parse(currentDocument.data);
          } catch (e) {
            logger.warn(
              `Failed to parse existing document data for ${documentId}: ${e}`
            );
            existingData = {};
          }
        } else {
          existingData = currentDocument.data;
        }
      }

      await Document.update((qb) => qb.where({ id: documentId }), {
        pinecone_file_id: `internal_${documentId}`,
        data: {
          ...existingData,
          vectorization: {
            status: result.errorCount === 0 ? "completed" : "partial",
            chunks_total: chunks.length,
            chunks_indexed: result.successCount,
            chunks_failed: result.errorCount,
            completed_at: new Date().toISOString(),
            // @ts-expect-error: Allow document_hash for backward compatibility
            document_hash: documentHash,
          },
        },
      });

      return result.successCount > 0;
    } catch (error) {
      logger.error(`Error processing document ${documentId}:`, error);

      // Update document with error status
      try {
        // Get current document data
        const currentDocument = await Document.first((qb) =>
          qb.where({ id: documentId })
        );

        // Parse existing data if it's a string
        let existingData = {};
        if (currentDocument?.data) {
          if (typeof currentDocument.data === "string") {
            try {
              existingData = JSON.parse(currentDocument.data);
            } catch (e) {
              logger.warn(
                `Failed to parse existing document data for ${documentId}: ${e}`
              );
              existingData = {};
            }
          } else {
            existingData = currentDocument.data;
          }
        }

        await Document.update((qb) => qb.where({ id: documentId }), {
          data: {
            ...existingData,
            vectorization_error:
              error instanceof Error ? error.message : String(error),
            vectorization_error_time: new Date().toISOString(),
          },
        });
      } catch (updateError) {
        logger.error(
          `Failed to update document ${documentId} status after error:`,
          updateError
        );
      }

      throw error;
    }
  }

  /**
   * Extract content from a file based on its type
   */
  private static async extractContent(
    filePath: string,
    fileName: string,
    fileType: string
  ): Promise<string> {
    // Determine file type from MIME type or file extension if not provided
    const mimeType = fileType || this.getMimeTypeFromFileName(fileName);
    const extension = path.extname(fileName).toLowerCase();

    logger.info(
      `Extracting content from ${fileName}, MIME: ${mimeType}, Extension: ${extension}`
    );

    if (!mimeType || mimeType === "undefined") {
      // If mimeType is undefined, try to infer from filename
      const inferredMimeType = this.getMimeTypeFromFileName(fileName);
      if (inferredMimeType && inferredMimeType !== "application/octet-stream") {
        logger.info(
          `Inferred MIME type ${inferredMimeType} from filename ${fileName}`
        );
        return await this.extractContent(filePath, fileName, inferredMimeType);
      }
      logger.warn(
        `Unable to determine file type for: ${fileName} (MIME: ${mimeType})`
      );
      return `Content from file ${fileName} could not be extracted due to unknown file type.`;
    }

    if (mimeType.includes("pdf") || extension === ".pdf") {
      return await this.extractFromPdf(filePath);
    } else if (
      mimeType.includes("word") ||
      [".docx", ".doc"].includes(extension)
    ) {
      return await this.extractFromWord(filePath);
    } else if (
      mimeType.includes("text") ||
      extension === ".txt" ||
      extension === ".md"
    ) {
      return await this.extractFromText(filePath);
    } else if (mimeType.includes("csv") || extension === ".csv") {
      return await this.extractFromCsv(filePath);
    } else if (
      mimeType.includes("spreadsheet") ||
      [".xlsx", ".xls"].includes(extension)
    ) {
      return await this.extractFromExcel(filePath);
    } else {
      // Default to treating as text for unknown types
      try {
        return await this.extractFromText(filePath);
      } catch {
        return `Content from file ${fileName} could not be extracted due to unsupported format.`;
      }
    }
  }

  /**
   * Extract content from a PDF file with improved memory management
   */
  private static async extractFromPdf(filePath: string): Promise<string> {
    // 🚀 PYTHON SERVICE: Allow up to 10MB since we're using Python microservice for processing
    const MAX_FILE_SIZE_MB = 10;
    const stats = fs.statSync(filePath);
    const fileSizeMB = stats.size / (1024 * 1024);

    if (fileSizeMB > MAX_FILE_SIZE_MB) {
      logger.warn(
        `PDF file too large (${fileSizeMB.toFixed(1)}MB > ${MAX_FILE_SIZE_MB}MB), using fallback extraction`
      );
      return `Content from file ${path.basename(
        filePath
      )}. This file was too large for detailed extraction (${fileSizeMB.toFixed(1)}MB). Maximum allowed: ${MAX_FILE_SIZE_MB}MB.`;
    }

    logger.info(`Processing PDF file: ${fileSizeMB.toFixed(1)}MB`);

    // 🛡️ MEMORY-EFFICIENT: Use streaming approach for better memory management
    let dataBuffer: Buffer | null = null;
    try {
      // For smaller files, read directly; for larger files, use streaming
      if (fileSizeMB <= 0.1) { // 0.1MB = 100KB
        // Direct read for small files
        dataBuffer = fs.readFileSync(filePath);
        logger.info(`PDF buffer created directly: ${(dataBuffer.length / 1024).toFixed(1)}KB`);
      } else {
        // Streaming read for larger files to reduce memory pressure
        const CHUNK_SIZE = 16384; // 16KB chunks for better performance
        const fileHandle = fs.openSync(filePath, 'r');
        const chunks: Buffer[] = [];
        let totalSize = 0;

        try {
          let position = 0;
          while (position < stats.size && totalSize < MAX_FILE_SIZE_MB * 1024 * 1024) {
            const remainingBytes = Math.min(CHUNK_SIZE, stats.size - position);
            const chunk = Buffer.alloc(remainingBytes);
            const bytesRead = fs.readSync(fileHandle, chunk, 0, remainingBytes, position);

            if (bytesRead === 0) break;

            chunks.push(chunk.subarray(0, bytesRead));
            totalSize += bytesRead;
            position += bytesRead;

            // Yield control periodically to prevent blocking
            if (chunks.length % 10 === 0) {
              await new Promise(resolve => setImmediate(resolve));
            }
          }
        } finally {
          fs.closeSync(fileHandle);
        }

        dataBuffer = Buffer.concat(chunks);
        logger.info(`PDF buffer created from ${chunks.length} chunks: ${(dataBuffer.length / 1024).toFixed(1)}KB`);

        // Clear chunks array immediately
        chunks.length = 0;
      }

      try {
        // 🧠 MEMORY-EFFICIENT: Use pdf-parse with optimized settings
        logger.info(`Starting PDF parsing for ${path.basename(filePath)}`);
        const data = await pdfParse(dataBuffer, {
          // Adaptive page limits based on file size for memory efficiency
          max: fileSizeMB <= 0.1 ? 20 : fileSizeMB <= 0.3 ? 10 : 5, // 0.1MB = 100KB, 0.3MB = 300KB
        });

        // 🧹 IMMEDIATE CLEANUP: Clear the buffer reference
        dataBuffer = null as any;

        const extractedText = data.text;
        logger.info(
          `PDF text extraction completed. Length: ${extractedText.length} characters`
        );

        // 🧹 MEMORY CLEANUP: Clear references to help garbage collection
        logger.debug(`Memory cleanup after PDF parsing completed`);

        // Force a small delay to allow garbage collection
        await new Promise(resolve => setTimeout(resolve, 100));

        // If no text was extracted, it might be a scanned PDF
        if (!extractedText || extractedText.trim().length === 0) {
          logger.warn(
            `No text extracted from PDF ${path.basename(
              filePath
            )} - likely a scanned document`
          );
          return `Content from PDF document ${path.basename(
            filePath
          )}. This appears to be a scanned document with no extractable text layer.`;
        } else {
          return extractedText;
        }
      } catch (error) {
        // 🧹 CLEANUP: Ensure buffer is cleared even on error
        dataBuffer = null as any;
        logger.debug(`Memory cleanup after PDF parsing error`);

        // Force a small delay to allow garbage collection
        await new Promise(resolve => setTimeout(resolve, 100));

        logger.error(`Error parsing PDF: ${error}`);

        // Try fallback approach for corrupted PDFs - extract whatever text we can
        try {
          logger.info(
            `Using fallback content extraction for ${path.basename(filePath)}`
          );

          // Return a placeholder message with the file name to allow processing to continue
          return `Content from file ${path.basename(
            filePath
          )}. This file appears to be corrupted or in an unsupported format. Processing with limited text extraction.`;
        } catch (fallbackError) {
          logger.error(`Fallback extraction also failed: ${fallbackError}`);
          throw new Error(
            `PDF extraction failed: ${
              error instanceof Error ? error.message : String(error)
            }`
          );
        }
      }
    } catch (fileError) {
      logger.error(`Failed to read PDF file: ${fileError}`);
      throw new Error(`Cannot read PDF file: ${fileError instanceof Error ? fileError.message : String(fileError)}`);
    } finally {
      // Force garbage collection after PDF processing to free memory
      if (global.gc) {
        global.gc();
        logger.debug(`Forced garbage collection after PDF processing`);
      }
    }
  }

  /**
   * Extract content from a Word document
   */
  private static async extractFromWord(filePath: string): Promise<string> {
    const buffer = fs.readFileSync(filePath);
    const result = await mammoth.extractRawText({ buffer });
    return result.value;
  }

  /**
   * Extract content from a plain text file
   */
  private static async extractFromText(filePath: string): Promise<string> {
    return fs.readFileSync(filePath, "utf8");
  }

  /**
   * Extract content from a CSV file
   */
  private static async extractFromCsv(filePath: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const rows: Record<string, string>[] = [];
      fs.createReadStream(filePath)
        .pipe(csv())
        .on("data", (data: Record<string, string>) => rows.push(data))
        .on("end", () => {
          if (rows.length === 0) {
            resolve("No data found in CSV file");
            return;
          }

          const headers = Object.keys(rows[0] || {});
          const content = [
            headers.join("\t"),
            ...rows.map((row) =>
              headers.map((header) => row[header]).join("\t")
            ),
          ].join("\n");
          resolve(content);
        })
        .on("error", (error: Error) => reject(error));
    });
  }

  /**
   * Extract content from an Excel file
   */
  private static async extractFromExcel(filePath: string): Promise<string> {
    const workbook = xlsx.readFile(filePath);
    const result: string[] = [];

    workbook.SheetNames.forEach((sheetName: string) => {
      const worksheet = workbook.Sheets[sheetName];
      const sheetData = xlsx.utils.sheet_to_json(worksheet);

      if (sheetData.length > 0) {
        // Add sheet header
        result.push(`--- Sheet: ${sheetName} ---`);

        // Cast the first row to Record<string, unknown> to avoid type errors
        const firstRow = sheetData[0] as Record<string, unknown>;
        const headers = Object.keys(firstRow);
        result.push(headers.join("\t"));

        // Add rows with proper type casting
        sheetData.forEach((row) => {
          const typedRow = row as Record<string, unknown>;
          result.push(
            headers.map((header) => String(typedRow[header] || "")).join("\t")
          );
        });

        result.push(""); // Add empty line between sheets
      }
    });

    return result.join("\n");
  }

  /**
   * Create chunks from content with proper overlap
   */
  private static createChunks(
    content: string,
    documentId: number,
    locationId: number,
    documentName: string,
    documentType: string
  ): DocumentChunk[] {
    if (!content || content.trim().length === 0) {
      return [];
    }

    const chunks: DocumentChunk[] = [];
    let startIndex = 0;
    let chunkIndex = 0;

    while (startIndex < content.length) {
      const endIndex = Math.min(startIndex + CHUNK_SIZE, content.length);
      const chunkContent = content.substring(startIndex, endIndex);

      if (chunkContent.trim().length === 0) {
        startIndex = endIndex - CHUNK_OVERLAP;
        continue;
      }

      chunks.push({
        id: `doc_${documentId}_chunk_${chunkIndex}`,
        content: chunkContent,
        metadata: {
          document_id: documentId,
          location_id: locationId,
          chunk_index: chunkIndex,
          total_chunks: 0, // Will be updated later
          document_name: documentName,
          document_type: documentType,
          created_at: Date.now(),
        },
      });

      chunkIndex++;
      startIndex = endIndex - CHUNK_OVERLAP;
    }

    // Update total_chunks field
    chunks.forEach((chunk) => {
      chunk.metadata.total_chunks = chunks.length;
    });

    return chunks;
  }

  /**
   * Search document chunks based on a query
   */
  static async searchDocumentChunksByIds(
    query: string,
    locationId: number,
    documentIds: number[],
    limit: number = 5
  ): Promise<DocumentChunk[]> {
    try {
      if (!query) {
        throw new Error("Empty query provided to document search");
      }
      if (!documentIds || documentIds.length === 0) {
        return [];
      }

      // Create a filter that includes the specific document_ids
      // This assumes the vector DB supports an `$in` operator for filtering
      const filter = {
        source_type: "document",
        document_id: { $in: documentIds },
      };

      const vectorService = this.getVectorService();
      const primaryNs = this.getNamespace(locationId);
      const altNs = `location_${locationId}`;

      const mapResults = (results: any[]) =>
        results.map((result) => ({
          id: result.id,
          content: result.metadata.content || result.metadata.text || "",
          score: typeof result.score === "number" ? result.score : undefined,
          metadata: {
            document_id: Number(result.metadata.document_id),
            location_id: Number(result.metadata.location_id),
            chunk_index: Number(result.metadata.chunk_index || 0),
            total_chunks: Number(result.metadata.total_chunks || 1),
            document_name: String(result.metadata.document_name || "Unknown"),
            document_type: String(result.metadata.document_type || "Unknown"),
            created_at: Number(result.metadata.created_at || Date.now()),
          },
        }));

      const embedStart = Date.now();
      // @ts-ignore
      const embedding = await (vectorService as any).embeddings.embedQuery(query);
      const precomputedTime = Date.now() - embedStart;

      // Attempt search in primary namespace
      let results = await vectorService.queryVectorsWithEmbedding(
        DOCUMENT_INDEX,
        embedding,
        filter,
        limit,
        primaryNs,
        precomputedTime
      );

      if (results.length > 0) {
        return mapResults(results);
      }

      // Attempt search in alternate namespace
      results = await vectorService.queryVectorsWithEmbedding(
        DOCUMENT_INDEX,
        embedding,
        filter,
        limit,
        altNs,
        precomputedTime
      );

      if (results.length > 0) {
        return mapResults(results);
      }

      // Fallback to text search on the specified documents
      logger.warn(`No vector results for docs ${documentIds.join(', ')}, using text fallback.`);
      const documents = await Document.query().where({ location_id: locationId }).whereIn('id', documentIds);
      
      const allRelevantChunks: DocumentChunk[] = [];
      for (const document of documents) {
        if (document && document.data?.content) {
          const content = document.data.content;
          const allChunks = this.createChunks(content, document.id, locationId, document.name, document.type);
          const relevantChunks = allChunks.filter(chunk => chunk.content.toLowerCase().includes(query.toLowerCase()));
          allRelevantChunks.push(...relevantChunks);
        }
      }
      
      return allRelevantChunks.slice(0, limit);
    } catch (error) {
      logger.error({
        message: "Error in specific document search by IDs",
        error: error instanceof Error ? error.message : String(error),
        document_ids: documentIds,
      });
      return [];
    }
  }

  static async searchDocumentChunks(
    query: string,
    locationId: number,
    limit: number = 5
  ): Promise<DocumentChunk[]> {
    try {
      if (!query) {
        throw new Error("Empty query provided to document search");
      }

      // Try vector search first (with namespace fallback: hyphen → underscore)
      try {
        const primaryNs = this.getNamespace(locationId); // e.g., location-123
        const altNs = `location_${locationId}`; // Python service style e.g., location_123

        // Helper to map VectorQueryResult[] to DocumentChunk[]
        const mapResults = (results: any[]) =>
          results.map((result) => ({
            id: result.id,
            content: result.metadata.content || result.metadata.text || "",
            score: typeof result.score === "number" ? result.score : undefined,
            metadata: {
              document_id: Number(result.metadata.document_id),
              location_id: Number(result.metadata.location_id),
              chunk_index: Number(result.metadata.chunk_index || 0),
              total_chunks: Number(result.metadata.total_chunks || 1),
              document_name: String(result.metadata.document_name || "Unknown"),
              document_type: String(result.metadata.document_type || "Unknown"),
              created_at: Number(result.metadata.created_at || Date.now()),
            },
          }));

        // Precompute embedding once to speed up multiple attempts
        const vectorService = this.getVectorService();
        const embedStart = Date.now();
        // Use the same embedder as VectorService; exposes queryVectorsWithEmbedding API
        // @ts-ignore - access embeddings via public method when available
        const embedding = await (vectorService as any).embeddings.embedQuery(query);
        const precomputedTime = Date.now() - embedStart;

        // First attempt: primary namespace (hyphen)
        let results = await vectorService.queryVectorsWithEmbedding(
          DOCUMENT_INDEX,
          embedding,
          { source_type: "document" },
          limit,
          primaryNs,
          precomputedTime
        );

        if (results.length > 0) {
          logger.info({
            message: "Document vector search succeeded",
            namespace: primaryNs,
            count: results.length,
          });
          return mapResults(results);
        }

        // Second attempt: alternate namespace (underscore) used by Python service
        logger.info({
          message: "Primary namespace returned no results, trying alternate namespace",
          primaryNs,
          altNs,
        });
        results = await vectorService.queryVectorsWithEmbedding(
          DOCUMENT_INDEX,
          embedding,
          { source_type: "document" },
          limit,
          altNs,
          precomputedTime
        );

        if (results.length > 0) {
          logger.info({
            message: "Document vector search succeeded with alternate namespace",
            namespace: altNs,
            count: results.length,
          });
          return mapResults(results);
        }

        // Third attempt: retry without strict filter in both namespaces in case external pipeline omitted source_type
        logger.info({ message: "No results with source_type filter; retrying without filter" });
        results = await vectorService.queryVectorsWithEmbedding(
          DOCUMENT_INDEX,
          embedding,
          {},
          limit,
          primaryNs,
          precomputedTime
        );
        if (results.length > 0) {
          logger.info({ message: "Succeeded without filter in primary namespace", count: results.length });
          return mapResults(results);
        }
        results = await vectorService.queryVectorsWithEmbedding(
          DOCUMENT_INDEX,
          embedding,
          {},
          limit,
          altNs,
          precomputedTime
        );
        if (results.length > 0) {
          logger.info({ message: "Succeeded without filter in alternate namespace", count: results.length });
          return mapResults(results);
        }

        // Global vector search without namespace as a final attempt (small topK)
        try {
          let gresults = await vectorService.queryVectorsWithEmbedding(
            DOCUMENT_INDEX,
            embedding,
            { source_type: "document" },
            limit,
            undefined,
            precomputedTime
          );
          if (gresults.length > 0) {
            logger.info({ message: "Global vector search (with filter) succeeded", count: gresults.length });
            return mapResults(gresults);
          }
          gresults = await vectorService.queryVectorsWithEmbedding(
            DOCUMENT_INDEX,
            embedding,
            {},
            limit,
            undefined,
            precomputedTime
          );
          if (gresults.length > 0) {
            logger.info({ message: "Global vector search (no filter) succeeded", count: gresults.length });
            return mapResults(gresults);
          }
        } catch (gErr) {
          logger.warn({ message: "Global vector search failed", error: gErr instanceof Error ? gErr.message : String(gErr) });
        }

        // If still nothing, fall back to text search in DB
        logger.warn({
          message: "No vector results found (namespaced or global), falling back to text search",
          primaryNs,
          altNs,
        });
        const localText = await this.searchDocumentContentFallback(query, locationId, limit);
        if (localText.length > 0) return localText;

        // Final fallback: global text search across documents (no location filter)
        try {
          logger.info("Using global text search fallback (no location filter)");
          const documents = await Document.all((qb: any) =>
            qb.where('status', 'completed')
              .whereRaw('LOWER(data->>\'content\') LIKE ?', [`%${query.toLowerCase()}%`])
              .limit(limit)
          );

          const chunks: DocumentChunk[] = [];
          for (const document of documents) {
            const content = document.data?.content || '';
            if (!content) continue;
            const documentChunks = this.createChunks(
              content,
              document.id,
              document.location_id,
              document.name,
              document.type
            );
            const relevantChunks = documentChunks.filter((chunk: DocumentChunk) =>
              chunk.content.toLowerCase().includes(query.toLowerCase())
            );
            chunks.push(...relevantChunks);
          }
          return chunks.slice(0, limit);
        } catch (gtErr) {
          logger.warn({ message: "Global text search fallback failed", error: gtErr instanceof Error ? gtErr.message : String(gtErr) });
          return [];
        }
      } catch (vectorError) {
        logger.warn("Vector search errored, falling back to text search:", vectorError);
        return await this.searchDocumentContentFallback(query, locationId, limit);
      }
    } catch (error) {
      logger.error({
        message: "Error in document search",
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        query,
        location_id: locationId,
      });
      return [];
    }
  }

  /**
   * Fallback text search when vector search is unavailable
   */
  private static async searchDocumentContentFallback(
    query: string,
    locationId: number,
    limit: number = 5
  ): Promise<DocumentChunk[]> {
    try {
      logger.info(`Using fallback text search for query: "${query}"`);

      // Search for documents that contain the query text
      const documents = await Document.all((qb: any) =>
        qb.where('location_id', locationId)
          .where('status', 'completed')
          .whereRaw('LOWER(data->>\'content\') LIKE ?', [`%${query.toLowerCase()}%`])
          .limit(limit)
      );

      const chunks: DocumentChunk[] = [];

      for (const document of documents) {
        const content = document.data?.content || '';
        if (!content) continue;

        // Create chunks from the document content
        const documentChunks = this.createChunks(
          content,
          document.id,
          locationId,
          document.name,
          document.type
        );

        // Filter chunks that contain the query
        const relevantChunks = documentChunks.filter((chunk: DocumentChunk) =>
          chunk.content.toLowerCase().includes(query.toLowerCase())
        );

        chunks.push(...relevantChunks);

        chunks.push(...relevantChunks);
      }

      // Sort by relevance (chunks with query appearing earlier)
      chunks.sort((a, b) => {
        const aIndex = a.content.toLowerCase().indexOf(query.toLowerCase());
        const bIndex = b.content.toLowerCase().indexOf(query.toLowerCase());
        return aIndex - bIndex;
      });

      logger.info(`Fallback search found ${chunks.length} relevant chunks for query: "${query}"`);

      // Log the first few chunks for debugging
      chunks.slice(0, 3).forEach((chunk, index) => {
        logger.info(`Chunk ${index + 1} preview: "${chunk.content.substring(0, 200)}..."`);
      });

      return chunks.slice(0, limit);
    } catch (error) {
      logger.error("Error in fallback document search:", error);
      return [];
    }
  }

  /**
   * Delete document vectors
   */
  static async deleteDocumentVectors(
    documentId: number,
    locationId: number
  ): Promise<boolean> {
    try {
      const namespace = this.getNamespace(locationId);

      // Delete vectors for this specific document within the location namespace
      // We delete both numeric and string document_id variants to cover different pipelines
      await this.getVectorService().deleteVectors(
        DOCUMENT_INDEX,
        { document_id: documentId },
        namespace
      );
      await this.getVectorService().deleteVectors(
        DOCUMENT_INDEX,
        { document_id: documentId.toString() as unknown as number }, // handled as metadata equality by Mongo store
        namespace
      );

      // Also delete vectors in the Python service namespace style (underscore)
      const altNamespace = `location_${locationId}`;
      await this.getVectorService().deleteVectors(
        DOCUMENT_INDEX,
        { document_id: documentId },
        altNamespace
      );
      await this.getVectorService().deleteVectors(
        DOCUMENT_INDEX,
        { document_id: documentId.toString() as unknown as number },
        altNamespace
      );

      logger.info(
        `Deleted vectors for document ${documentId} in location ${locationId}`
      );
      return true;
    } catch (error) {
      logger.error({
        message: "Error deleting document vectors",
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        document_id: documentId,
        location_id: locationId,
      });
      return false;
    }
  }

  /**
   * Reindex all documents for a location
   * This starts a background process to reindex all documents for a location
   */
  static async reindexLocationDocuments(
    locationId: number
  ): Promise<{ jobId: string; documentCount: number }> {
    try {
      logger.info(
        `Starting reindex of all documents for location ${locationId}`
      );

      // Get all documents for the location
      const documents = await Document.query()
        .where({ location_id: locationId })
        .whereNull("deleted_at")
        .orderBy("created_at", "desc");

      if (!documents || documents.length === 0) {
        logger.info(`No documents found for location ${locationId}`);
        return { jobId: "none", documentCount: 0 };
      }

      logger.info(
        `Found ${documents.length} documents to reindex for location ${locationId}`
      );

      // Create a job tracker entry
      const jobId = `reindex-loc-${locationId}-${Date.now()}`;

      // 🔧 FIX: Don't create duplicate job tracker entries here
      // Individual DocumentVectorJob instances will handle their own tracking

      // Queue jobs for each document
      let queuedCount = 0;
      for (const document of documents) {
        try {
          // Create and queue the job
          await DocumentVectorJob.from({
            document_id: document.id,
            location_id: locationId,
          }).queue();

          queuedCount++;
        } catch (queueError) {
          logger.error(
            `Error queueing reindex job for document ${document.id}: ${queueError}`
          );
          // Continue with other documents
        }
      }

      logger.info(
        `Queued ${queuedCount} document vectorization jobs for location ${locationId}`
      );

      return {
        jobId,
        documentCount: documents.length,
      };
    } catch (error) {
      logger.error({
        message: "Error starting document reindexing",
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        location_id: locationId,
      });
      throw error;
    }
  }

  /**
   * Get vectorization status for a location (wrapper for getLocationIndexingStatus)
   * Returns information about document vectorization progress in the expected format
   */
  static async getVectorizationStatus(locationId: number): Promise<{
    status: "completed" | "in_progress" | "failed" | "not_started";
    indexedCount: number;
    inProgressCount: number;
    failedCount: number;
    notStartedCount: number;
  }> {
    const indexingStatus = await this.getLocationIndexingStatus(locationId);

    return {
      status: indexingStatus.status,
      indexedCount: indexingStatus.indexed_documents,
      inProgressCount: indexingStatus.in_progress_documents,
      failedCount: indexingStatus.failed_documents,
      notStartedCount: indexingStatus.total_documents - indexingStatus.indexed_documents - indexingStatus.failed_documents - indexingStatus.in_progress_documents,
    };
  }

  /**
   * Get indexing status for a location
   * Returns information about document indexing progress
   */
  static async getLocationIndexingStatus(locationId: number): Promise<{
    total_documents: number;
    indexed_documents: number;
    failed_documents: number;
    in_progress_documents: number;
    status: "not_started" | "in_progress" | "completed" | "failed";
    last_updated: string;
  }> {
    try {
      logger.info(
        `Getting document indexing status for location ${locationId}`
      );

      // Get all documents for the location
      const documents = await Document.query()
        .where({ location_id: locationId })
        .whereNull("deleted_at");

      if (!documents || documents.length === 0) {
        return {
          total_documents: 0,
          indexed_documents: 0,
          failed_documents: 0,
          in_progress_documents: 0,
          status: "not_started",
          last_updated: new Date().toISOString(),
        };
      }

      // Count documents in different states
      let indexedCount = 0;
      let failedCount = 0;
      let inProgressCount = 0;
      let lastUpdated = new Date(0).toISOString();

      for (const doc of documents) {
        // Check if document has been vectorized by looking at document data
        const docData =
          typeof doc.data === "string"
            ? JSON.parse(doc.data || "{}")
            : doc.data || {};

        const vectorization = docData.vectorization || {};

        if (vectorization.status === "completed") {
          indexedCount++;
        } else if (
          vectorization.status === "failed" ||
          docData.vectorization_error
        ) {
          failedCount++;
        } else if (doc.status === "processing" || doc.status === "pending") {
          inProgressCount++;
        }

        // Track the most recent update
        const completedAt = vectorization.completed_at;
        const errorTime = docData.vectorization_error_time;

        if (completedAt && completedAt > lastUpdated) {
          lastUpdated = completedAt;
        }

        if (errorTime && errorTime > lastUpdated) {
          lastUpdated = errorTime;
        }
      }

      // Determine overall status
      let status: "not_started" | "in_progress" | "completed" | "failed";

      if (indexedCount === documents.length) {
        status = "completed";
      } else if (indexedCount === 0 && failedCount === documents.length) {
        status = "failed";
      } else if (inProgressCount > 0 || indexedCount > 0) {
        status = "in_progress";
      } else {
        status = "not_started";
      }

      // Check onboarding job tracker for more status info
      try {
        // 🧹 CLEANUP: Remove stuck jobs that have been processing for too long
        const cleanedCount = OnboardingJobTracker.cleanupStuckJobs(locationId, 30); // 30 minutes timeout
        if (cleanedCount > 0) {
          logger.info(`Cleaned up ${cleanedCount} stuck document processing jobs for location ${locationId}`);
        }

        const onboardingStatus = OnboardingJobTracker.getSummary(locationId);
        const docJobs = onboardingStatus.jobs.filter(
          (j: OnboardingJob) =>
            j.jobType === "document_vectorization" ||
            j.jobType.startsWith("document_upload_")
        );

        if (docJobs.length > 0) {
          // If we have job information, use it to complement our status
          const processingJobs = docJobs.filter(
            (j: OnboardingJob) => j.status === "processing"
          );

          if (processingJobs.length > 0 && status !== "completed") {
            status = "in_progress";
            inProgressCount = Math.max(inProgressCount, processingJobs.length);
          }
        }
      } catch (trackerError) {
        logger.warn(`Could not get job tracker info: ${trackerError}`);
        // Continue without this additional information
      }

      return {
        total_documents: documents.length,
        indexed_documents: indexedCount,
        failed_documents: failedCount,
        in_progress_documents: inProgressCount,
        status,
        last_updated:
          lastUpdated !== new Date(0).toISOString()
            ? lastUpdated
            : new Date().toISOString(),
      };
    } catch (error) {
      logger.error({
        message: "Error getting document indexing status",
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        location_id: locationId,
      });

      // Return a default error state
      return {
        total_documents: 0,
        indexed_documents: 0,
        failed_documents: 0,
        in_progress_documents: 0,
        status: "failed",
        last_updated: new Date().toISOString(),
      };
    }
  }

  /**
   * Convert a location ID to a Pinecone namespace
   */
  private static getNamespace(locationId: number): string {
    return `location-${locationId}`;
  }

  /**
   * Get MIME type from file name
   */
  private static getMimeTypeFromFileName(fileName: string): string {
    const extension = path.extname(fileName).toLowerCase();

    const mimeTypes: Record<string, string> = {
      ".pdf": "application/pdf",
      ".doc": "application/msword",
      ".docx":
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      ".txt": "text/plain",
      ".md": "text/markdown",
      ".csv": "text/csv",
      ".xls": "application/vnd.ms-excel",
      ".xlsx":
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    };

    return mimeTypes[extension] || "application/octet-stream";
  }

  /**
   * Ensures that the vector index exists and creates it if needed
   */
  static async ensureIndexExists(): Promise<boolean> {
    try {
      await this.getVectorService().initialize();

      // Check if index exists first by listing indices
      const indices = await this.getVectorService().listIndices();
      const indexExists = indices.includes(DOCUMENT_INDEX);

      if (!indexExists) {
        logger.info(
          `Document index '${DOCUMENT_INDEX}' not found, creating it`
        );

        // Create index with 1536 dimensions for text-embedding-3-small
        await this.getVectorService().createIndex(DOCUMENT_INDEX, {
          dimension: 1536,
          metric: "cosine",
          serverless: {
            cloud: "aws",
            region: "us-east-1",
          },
        });

        logger.info(`Document index '${DOCUMENT_INDEX}' created successfully`);
      } else {
        logger.info(`Document index '${DOCUMENT_INDEX}' already exists`);
      }

      return true;
    } catch (error) {
      logger.error(`Failed to verify document index: ${error}`);
      return false;
    }
  }
}
