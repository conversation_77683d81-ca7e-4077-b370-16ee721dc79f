import crypto from "crypto";
import fetch from "node-fetch";
import { OpenAIEmbeddings } from "@langchain/openai";
import { logger } from "../config/logger";
import { MongoVectorStore } from "../core/MongoVectorStore";
import { EMBEDDING_MODEL, EMBEDDING_DIMENSIONS } from "../core/EmbeddingConfig";

export interface KBIngestResult {
  success: boolean;
  ingestedCount: number;
  skippedCount: number;
  details: Array<{ url: string; ok: boolean; text_length?: number; error?: string; skipped?: boolean }>
}

export interface KBSearchResult {
  id: string;
  score: number;
  content: string;
  metadata: Record<string, any>;
}

export interface KBIngestOptions {
  locationId?: number;
  superUser?: boolean;
}

export interface KBSearchOptions {
  locationId?: number;
  superUser?: boolean;
}

const KB_INDEX = "document-embeddings"; // reuse same collection name
const KB_NAMESPACE = "kb"; // global namespace

export class KnowledgeBaseService {
  private static instance: KnowledgeBaseService;
  private embeddings: OpenAIEmbeddings;
  private mongo: MongoVectorStore;
  private initialized = false;
  private pythonServiceUrl: string;

  private constructor() {
    if (!process.env.OPENAI_API_KEY) {
      throw new Error("OPENAI_API_KEY not set");
    }
    const mongoUri = process.env.MONGODB_URI;
    const kbDb = process.env.MONGODB_KB_DB_NAME || "bakedbot_kb";
    if (!mongoUri) {
      throw new Error("MONGODB_URI not set");
    }
    this.mongo = new MongoVectorStore(mongoUri, kbDb);
    this.embeddings = new OpenAIEmbeddings({
      openAIApiKey: process.env.OPENAI_API_KEY,
      modelName: EMBEDDING_MODEL,
    });
    this.pythonServiceUrl = process.env.PYTHON_SERVICE_URL || "http://document-processor:8000";
  }

  static getInstance(): KnowledgeBaseService {
    if (!KnowledgeBaseService.instance) {
      KnowledgeBaseService.instance = new KnowledgeBaseService();
    }
    return KnowledgeBaseService.instance;
  }

  async initialize(): Promise<void> {
    if (this.initialized) return;
    await this.mongo.initialize();
    await this.mongo.ensureIndex(KB_INDEX, EMBEDDING_DIMENSIONS);
    this.initialized = true;
    logger.info("KnowledgeBaseService initialized (db=bakedbot_kb)");
  }

  private sha256(input: string): string {
    return crypto.createHash("sha256").update(input).digest("hex");
  }

  private chunkText(content: string, chunkSize = 1000, overlap = 200): string[] {
    const chunks: string[] = [];
    let start = 0;
    const len = content.length;
    while (start < len) {
      const end = Math.min(start + chunkSize, len);
      const chunk = content.substring(start, end);
      if (chunk.trim().length > 0) chunks.push(chunk);
      if (end === len) break;
      start = end - overlap;
    }
    return chunks;
  }

  async ingestUrls(urls: string[], opts: KBIngestOptions = {}): Promise<KBIngestResult> {
    await this.initialize();

    // Normalize and de-duplicate input URLs up front
    const normalized = Array.from(new Set(urls.map(u => String(u).trim()).filter(Boolean)));

    // Call Python service to fetch and extract text
    const endpoint = `${this.pythonServiceUrl}/api/v1/ingest-urls`;
    const res = await fetch(endpoint, { method: "POST", headers: { "Content-Type": "application/json" }, body: JSON.stringify({ urls: normalized }) });
    if (!res.ok) throw new Error(`Python ingest-urls failed: ${res.status}`);
    const data = await res.json();
    const results: Array<{ url: string; ok: boolean; text?: string; text_length?: number; error?: string }> = data.results || [];

    const docsToUpsert: { _id: string; embedding: number[]; metadata: Record<string, any>; namespace: string }[] = [];
    let ingestedCount = 0;
    let skippedCount = 0;
    const detailsOut: Array<{ url: string; ok: boolean; text_length?: number; error?: string; skipped?: boolean }> = [];

    for (const res of results) {
      if (!res.ok || !res.text) {
        skippedCount++;
        detailsOut.push({ url: res.url, ok: false, error: res.error });
        continue;
      }
      const url = res.url;
      const text = res.text;
      const contentHash = this.sha256(text);

      // If existing content hash matches, skip re-embedding to save cost and avoid duplicates
      const ns = opts?.superUser ? KB_NAMESPACE : (opts?.locationId ? `kb_loc_${opts.locationId}` : KB_NAMESPACE);
      const alreadyUpToDate = await this.mongo.exists(
        KB_INDEX,
        { source_type: "kb_web", source_url: url, content_hash: contentHash },
        ns
      );
      if (alreadyUpToDate) {
        // Still count as ok in details, but mark as skipped
        skippedCount++;
        detailsOut.push({ url, ok: true, text_length: res.text_length, skipped: true });
        continue;
      }

      const textChunks = this.chunkText(text);

      // Clean previous entries for this URL to prevent duplicates/stale content growth
      await this.mongo.deleteByFilter(KB_INDEX, { source_type: "kb_web", source_url: url }, ns);

      // Embed chunks in small batches
      for (let i = 0; i < textChunks.length; i += 5) {
        const batch = textChunks.slice(i, i + 5);
        const embeddings = await this.embeddings.embedDocuments(batch);
        for (let j = 0; j < batch.length; j++) {
          const chunkIndex = i + j;
          // Deterministic id to prevent duplicates per URL+chunk index
          const id = `kb_${this.sha256(`${url}|${chunkIndex}`)}`;
          docsToUpsert.push({
            _id: id,
            embedding: embeddings[j],
            metadata: {
              source_type: "kb_web",
              source_url: url,
              content: batch[j],
              content_hash: contentHash,
              chunk_index: chunkIndex,
              created_at: Date.now(),
              updated_at: Date.now(),
              ...(opts?.locationId ? { location_id: opts.locationId } : {}),
            },
            namespace: ns,
          });
        }
      }
      ingestedCount++;
      detailsOut.push({ url, ok: true, text_length: res.text_length, skipped: false });
    }

    if (docsToUpsert.length > 0) {
      const ns = opts?.superUser ? KB_NAMESPACE : (opts?.locationId ? `kb_loc_${opts.locationId}` : KB_NAMESPACE);
      await this.mongo.upsert(KB_INDEX, docsToUpsert as any, ns);
    }

    return {
      success: true,
      ingestedCount,
      skippedCount,
      details: detailsOut,
    };
  }

  async search(query: string, topK = 5, opts: KBSearchOptions = {}): Promise<KBSearchResult[]> {
    await this.initialize();
    const embedding = await this.embeddings.embedQuery(query);
    const ns = opts?.superUser ? KB_NAMESPACE : (opts?.locationId ? `kb_loc_${opts.locationId}` : KB_NAMESPACE);
    const results = await this.mongo.query(KB_INDEX, embedding, { source_type: "kb_web" }, topK, ns);
    return results.map(r => ({ id: r.id, score: r.score, content: (r.metadata as any).content || "", metadata: r.metadata as any }));
  }
}

export const knowledgeBaseService = KnowledgeBaseService.getInstance();
