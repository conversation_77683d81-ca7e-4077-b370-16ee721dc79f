import App from "../../app";
import { logger } from "../../config/logger";
import EmailChannel from "./EmailChannel";
import GlobalSendGridProvider from "./GlobalSendGridProvider";
import GlobalSMTPProvider from "./GlobalSMTPProvider";

/**
 * Global Email Service
 * Provides email functionality using environment variables instead of database providers
 */
export class GlobalEmailService {
  /**
   * Get the best available global email provider
   */
  static async getGlobalEmailProvider(): Promise<EmailChannel | null> {
    try {
      // Try SendGrid first (preferred)
      if (App.main.env.email.sendgrid.apiKey) {
        logger.info("🌍 Using Global SendGrid provider");
        const provider = new GlobalSendGridProvider();
        provider.boot();
        return new EmailChannel(provider);
      }

      // Fallback to SMTP if configured
      if (
        App.main.env.email.smtp.host &&
        App.main.env.email.smtp.port &&
        App.main.env.email.smtp.user &&
        App.main.env.email.smtp.pass
      ) {
        logger.info("🌍 Using Global SMTP provider");
        const provider = new GlobalSMTPProvider();
        provider.boot();
        return new EmailChannel(provider);
      }

      logger.warn("❌ No global email providers configured");
      logger.warn("💡 Configure SENDGRID_API_KEY or SMTP_* environment variables");
      return null;
    } catch (error: any) {
      logger.error("❌ Failed to load global email provider:", error.message);
      return null;
    }
  }

  /**
   * Check if any global email provider is available
   */
  static isGlobalEmailAvailable(): boolean {
    return !!(
      App.main.env.email.sendgrid.apiKey ||
      (App.main.env.email.smtp.host &&
        App.main.env.email.smtp.port &&
        App.main.env.email.smtp.user &&
        App.main.env.email.smtp.pass)
    );
  }

  /**
   * Get configuration status for debugging
   */
  static getConfigStatus() {
    return {
      sendgrid: {
        configured: !!App.main.env.email.sendgrid.apiKey,
        apiKey: App.main.env.email.sendgrid.apiKey
          ? `${App.main.env.email.sendgrid.apiKey.slice(0, 10)}...${App.main.env.email.sendgrid.apiKey.slice(-10)}`
          : "Not configured",
      },
      smtp: {
        configured: !!(
          App.main.env.email.smtp.host &&
          App.main.env.email.smtp.port &&
          App.main.env.email.smtp.user &&
          App.main.env.email.smtp.pass
        ),
        host: App.main.env.email.smtp.host || "Not configured",
        port: App.main.env.email.smtp.port || "Not configured",
        user: App.main.env.email.smtp.user || "Not configured",
        secure: App.main.env.email.smtp.secure ?? false,
      },
    };
  }
}
