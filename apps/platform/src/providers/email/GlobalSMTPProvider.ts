import nodemailer from "nodemailer";
import <PERSON>ailProvider from "./EmailProvider";
import App from "../../app";
import { logger } from "../../config/logger";

/**
 * Global SMTP Email Provider
 * Uses environment variables instead of database configuration
 */
export default class GlobalSMTPProvider extends EmailProvider {
  static namespace = "global-smtp";
  static meta = {
    name: "SMTP (Global)",
    icon: "https://beta.bakedbot.ai/images/providers/smtp.svg",
    isGlobal: true,
  };

  static group = "email" as const;

  get host(): string {
    const host = App.main.env.email.smtp.host;
    if (!host) {
      throw new Error("SMTP_HOST environment variable is not configured");
    }
    return host;
  }

  get port(): number {
    const port = App.main.env.email.smtp.port;
    if (!port) {
      throw new Error("SMTP_PORT environment variable is not configured");
    }
    return port;
  }

  get secure(): boolean {
    return App.main.env.email.smtp.secure ?? false;
  }

  get user(): string {
    const user = App.main.env.email.smtp.user;
    if (!user) {
      throw new Error("SMTP_USER environment variable is not configured");
    }
    return user;
  }

  get pass(): string {
    const pass = App.main.env.email.smtp.pass;
    if (!pass) {
      throw new Error("SMTP_PASS environment variable is not configured");
    }
    return pass;
  }

  boot() {
    this.transport = nodemailer.createTransport({
      host: this.host,
      port: this.port,
      secure: this.secure,
      auth: {
        user: this.user,
        pass: this.pass,
      },
    });

    logger.info("=== GLOBAL SMTP PROVIDER ===");
    logger.info(`📧 SMTP configured: ${this.user}@${this.host}:${this.port}`);
    logger.info(`   Secure: ${this.secure}`);
  }

  async verify(): Promise<boolean> {
    try {
      if (!this.transport) {
        this.boot();
      }
      await this.transport?.verify();
      logger.info("✅ Global SMTP verification successful");
      return true;
    } catch (error: any) {
      logger.error("❌ Global SMTP verification failed:", error.message);
      return false;
    }
  }

  // Global providers don't need database setup
  loadSetup(): any[] {
    return [];
  }

  // Global providers don't have controllers
  static controllers() {
    return {};
  }
}
