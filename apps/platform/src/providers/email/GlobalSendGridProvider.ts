import sgMail from "@sendgrid/mail";
import EmailProvider from "./EmailProvider";
import App from "../../app";
import { Email } from "./Email";
import { logger } from "../../config/logger";

/**
 * Global SendGrid Email Provider
 * Uses environment variables instead of database configuration
 */
export default class GlobalSendGridProvider extends EmailProvider {
  static namespace = "global-sendgrid";
  static meta = {
    name: "SendGrid (Global)",
    url: "https://sendgrid.com",
    icon: "https://beta.bakedbot.ai/images/providers/sendgrid.svg",
    isGlobal: true,
  };

  static group = "email" as const;

  get api_key(): string {
    const apiKey = App.main.env.email.sendgrid.apiKey;
    if (!apiKey) {
      throw new Error("SENDGRID_API_KEY environment variable is not configured");
    }
    return apiKey;
  }

  boot() {
    sgMail.setApiKey(this.api_key);
  }

  async send(message: Email): Promise<any> {
    const transformedMessage = {
      ...message,
      from:
        typeof message.from === "string" ? message.from : message.from.address, // SendGrid SDK expects string format
      custom_args: message.headers,
      unique_args: message.headers,
      // Transform attachments to ensure content is string (base64) for SendGrid
      attachments: message.attachments?.map((attachment) => ({
        ...attachment,
        content: Buffer.isBuffer(attachment.content)
          ? attachment.content.toString("base64")
          : attachment.content,
      })),
    };

    logger.info("=== GLOBAL SENDGRID PROVIDER ===");
    logger.info(`📧 Sending email to: ${message.to}`);
    logger.info(`   Subject: ${message.subject}`);
    logger.info(`   From: ${message.from}`);
    logger.info(`   API Key: ${this.api_key.slice(0, 10)}...${this.api_key.slice(-10)}`);

    try {
      // Use direct SendGrid SDK instead of nodemailer
      const result = await sgMail.send(transformedMessage);
      logger.info(
        `✅ Global SendGrid send SUCCESS: ${result[0]?.statusCode} ${result[0]?.headers?.["x-message-id"]}`
      );
      return result;
    } catch (error: any) {
      logger.error(`❌ Global SendGrid send FAILED: ${error.message}`);
      if (error.response) {
        logger.error("Error response:", error.response.body);
      }
      throw error;
    }
  }

  async verify(): Promise<boolean> {
    try {
      // Test with a simple API call to verify the key works
      if (!this.api_key || !this.api_key.startsWith("SG.")) {
        return false;
      }

      return true;
    } catch (error: any) {
      logger.error("Global SendGrid verification failed:", {
        error: error.message,
        maskedKey: this.api_key
          ? `${this.api_key.slice(0, 4)}...${this.api_key.slice(-4)}`
          : "undefined",
      });
      return false;
    }
  }

  // Global providers don't need database setup
  loadSetup(): any[] {
    return [];
  }

  // Global providers don't have controllers
  static controllers() {
    return {};
  }
}
