import { TextProvider } from "./TextProvider";
import { InboundTextMessage, TextMessage, TextResponse } from "./TextMessage";
import App from "../../app";
import { logger } from "../../config/logger";

export default class GlobalLeafbuyerProvider extends TextProvider {
  static namespace = "global-leafbuyer";
  static meta = {
    name: "Leafbuyer (Global)",
    description: "Global Leafbuyer SMS provider using environment variables",
    url: "https://leafbuyer.com",
    icon: "https://beta.bakedbot.ai/images/providers/leafbuyer.svg",
    isGlobal: true,
  };

  static group = "text" as const;

  private tokenCache: { access_token: string; expires_at: number } | null = null;

  private get client_id(): string {
    const v = App.main.env.sms.leafbuyer?.clientId;
    if (!v) throw new Error("LEAFBUYER_CLIENT_ID environment variable is not configured");
    return v;
  }

  private get client_secret(): string {
    const v = App.main.env.sms.leafbuyer?.clientSecret;
    if (!v) throw new Error("LEAFBUYER_CLIENT_SECRET environment variable is not configured");
    return v;
  }

  private get audience(): string {
    return App.main.env.sms.leafbuyer?.audience || "api";
  }

  private get auth_url(): string {
    return App.main.env.sms.leafbuyer?.authUrl || "https://leafbuyer.auth0.com/oauth/token";
  }

  private get adhoc_url(): string {
    return App.main.env.sms.leafbuyer?.adhocUrl || "https://adhoc.leafbuyerloyalty.com/";
  }

  private get mode(): string {
    return App.main.env.sms.leafbuyer?.mode || "pin";
  }

  private get pin(): string | undefined {
    return App.main.env.sms.leafbuyer?.pin || undefined;
  }

  private get templateId(): string | undefined {
    return App.main.env.sms.leafbuyer?.templateId || undefined;
  }

  private async getAccessToken(): Promise<string> {
    const now = Math.floor(Date.now() / 1000);
    if (this.tokenCache && this.tokenCache.expires_at - 60 > now) {
      return this.tokenCache.access_token;
    }

    const form = new URLSearchParams();
    form.append("grant_type", "client_credentials");
    form.append("client_id", this.client_id);
    form.append("client_secret", this.client_secret);
    form.append("audience", this.audience);

    const res = await fetch(this.auth_url, {
      method: "POST",
      headers: { "content-type": "application/x-www-form-urlencoded" },
      body: form,
    });
    if (!res.ok) {
      const text = await res.text();
      throw new Error(`Leafbuyer OAuth failed: ${res.status} ${text}`);
    }
    const json: any = await res.json();
    const expires_in = typeof json.expires_in === "number" ? json.expires_in : 3600;
    this.tokenCache = {
      access_token: json.access_token,
      expires_at: now + expires_in,
    };
    return json.access_token;
  }

  // Prepare a Leafbuyer S3 location for campaign image upload and return signed URL + imageId
  private async prepareCampaignImageUpload(token: string): Promise<{ signedUploadUrl: string; imageId: string }> {
    const query = `mutation {\n  prepareCampaignImageUploadForSubjectLocation {\n    signedUploadUrl\n    imageId\n  }\n}`;
    const res = await fetch(this.adhoc_url, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        "User-Agent": "bakedbot/v1 (+https://github.com/dashon/smokey-ai)",
      },
      body: JSON.stringify({ query, variables: {} }),
    });

    const payload = await res.json().catch(() => ({} as any));
    const data = payload?.data?.prepareCampaignImageUploadForSubjectLocation;
    if (!res.ok || !data?.signedUploadUrl || !data?.imageId) {
      const errorMsg = payload?.errors?.[0]?.message || `Leafbuyer prepare image upload failed: ${res.status}`;
      throw new Error(errorMsg);
    }
    return { signedUploadUrl: data.signedUploadUrl, imageId: data.imageId };
  }

  private async fetchImageBuffer(url: string): Promise<{ buffer: Buffer; contentType: string }> {
    const resp = await fetch(url);
    if (!resp.ok) {
      const text = await resp.text().catch(() => "");
      throw new Error(`Failed to fetch media_url: ${resp.status} ${text}`);
    }
    const contentType = resp.headers.get("content-type") || "image/jpeg";
    const arrayBuffer = await resp.arrayBuffer();
    return { buffer: Buffer.from(arrayBuffer), contentType };
  }

  private async uploadToSignedUrl(signedUrl: string, buffer: Buffer, contentType: string) {
    const put = await fetch(signedUrl, {
      method: "PUT",
      headers: { "Content-Type": contentType },
      body: buffer as any,
    });
    if (!put.ok) {
      const text = await put.text().catch(() => "");
      throw new Error(`Failed to upload image to Leafbuyer S3: ${put.status} ${text}`);
    }
  }

  async send(message: TextMessage): Promise<TextResponse> {
    const { to } = message;

    const token = await this.getAccessToken();
    const effectiveTemplateId = message.templateId || this.templateId || null;
    const useTemplate = !!effectiveTemplateId;

    const firstName = message.firstName ? `"${message.firstName}"` : "null";
    const lastName = message.lastName ? `"${message.lastName}"` : "null";
    const birthDate = message.birthDate ? `"${message.birthDate}"` : "null";
    const pinValue = this.pin ? `"${this.pin}"` : "null";
    const modeValue = this.mode || "pin";
    const redirectUrl = message.redirectUrl ? `"${message.redirectUrl}"` : "null"; // quoted or null for GraphQL

    // If media_url is present, upload to Leafbuyer S3 and include imageId for MMS
    let imageIdGraphQL = "null";
    if (message.media_url) {
      try {
        const { signedUploadUrl, imageId } = await this.prepareCampaignImageUpload(token);
        const { buffer, contentType } = await this.fetchImageBuffer(message.media_url);
        await this.uploadToSignedUrl(signedUploadUrl, buffer, contentType);
        imageIdGraphQL = `"${imageId}"`;
      } catch (err) {
        logger.error({ err }, "Global Leafbuyer MMS image upload failed");
        throw err;
      }
    }

    const webhookUrl = `${App.main.env.baseUrl || "https://beta.bakedbot.ai"}/api/leafbuyer/webhooks/adhoc`;
    const mutation = `mutation {\n  sendAdHocMarketingMessage(input: {\n    phoneNumber: "${to}",\n    messageBody: ${JSON.stringify(message.text || "")},\n    imageId: ${imageIdGraphQL},\n    customMetadata: ${JSON.stringify(webhookUrl)},\n    firstName: ${firstName},\n    lastName: ${lastName},\n    birthDate: ${birthDate},\n    pin: ${pinValue},\n    mode: "${modeValue}",\n    templateId: ${useTemplate ? `"${effectiveTemplateId}"` : "null"},\n    redirectUrl: ${redirectUrl}\n  }) { status, dispatchId }\n}`;

    logger.info("=== GLOBAL LEAFBUYER PROVIDER ===");
    logger.info(`📱 Sending ${message.media_url ? "MMS" : "SMS"} to: ${to}`);
    logger.info(`   Mode: ${modeValue}${this.pin ? " (with PIN)" : ""}`);
    if (useTemplate) {
      logger.info(`   Template ID: ${this.templateId}`);
    }

    const res = await fetch(this.adhoc_url, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        "User-Agent": "bakedbot/v1 (+https://github.com/dashon/smokey-ai)",
      },
      body: JSON.stringify({ query: mutation, variables: {} }),
    });

    const payload = await res.json().catch(() => ({} as any));
    const data = payload?.data?.sendAdHocMarketingMessage;
    if (res.ok && data && data.status) {
      return {
        message,
        success: true,
        response: data.dispatchId || data.status,
      };
    }

    const errorMsg = payload?.errors?.[0]?.message || `Leafbuyer send failed: ${res.status}`;
    throw new Error(errorMsg);
  }

  parseInbound(_inbound: any): InboundTextMessage {
    return { to: "", from: "", text: "" };
  }

  // Global providers don't need database setup
  loadSetup(): any[] {
    return [];
  }

  // Global providers don't have controllers
  static controllers() {
    return {};
  }
}
