import App from "../../app";
import { logger } from "../../config/logger";
import TextChannel from "./TextChannel";
import GlobalLeafbuyerProvider from "./GlobalLeafbuyerProvider";

/**
 * Global SMS Service
 * Provides SMS functionality using environment variables instead of database providers
 */
export class GlobalSMSService {
  /**
   * Get the global SMS provider
   */
  static async getGlobalSMSProvider(): Promise<TextChannel | null> {
    try {
      // Prefer Leafbuyer if configured (compliance)
      if (
        App.main.env.sms.leafbuyer?.clientId &&
        App.main.env.sms.leafbuyer?.clientSecret
      ) {
        logger.info("🌍 Using Global Leafbuyer provider");
        const provider = new GlobalLeafbuyerProvider();
        return new TextChannel(provider);
      }

      logger.warn("❌ No global SMS providers configured");
      logger.warn("💡 Configure LEAFBUYER_* environment variables");
      return null;
    } catch (error: any) {
      logger.error("❌ Failed to load global SMS provider:", error.message);
      return null;
    }
  }

  /**
   * Check if global SMS provider is available
   */
  static isGlobalSMSAvailable(): boolean {
    return !!(
      App.main.env.sms.leafbuyer?.clientId && App.main.env.sms.leafbuyer?.clientSecret
    );
  }

  /**
   * Get configuration status for debugging
   */
  static getConfigStatus() {
    return {
      leafbuyer: {
        configured: !!(
          App.main.env.sms.leafbuyer?.clientId && App.main.env.sms.leafbuyer?.clientSecret
        ),
        clientId: App.main.env.sms.leafbuyer?.clientId
          ? `${App.main.env.sms.leafbuyer?.clientId.slice(0, 6)}...${App.main.env.sms.leafbuyer?.clientId.slice(-4)}`
          : "Not configured",
        mode: App.main.env.sms.leafbuyer?.mode || "pin",
        templateId: App.main.env.sms.leafbuyer?.templateId || "None",
      },
    };
  }
}
