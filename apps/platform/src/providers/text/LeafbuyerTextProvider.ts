import App from "../../app";
import { ExternalProviderParams, ProviderControllers, ProviderSchema, ProviderSetupMeta } from "../Provider";
import { createController } from "../ProviderService";
import { InboundTextMessage, TextMessage, TextResponse } from "./TextMessage";
import { TextProvider } from "./TextProvider";
import { logger } from "../../config/logger";
import Location from "../../locations/Location";

interface LeafbuyerDataParams {
  client_id: string;
  client_secret: string;
  auth_url?: string; // default https://leafbuyer.auth0.com/oauth/token
  audience?: string; // default "api"
  adhoc_url?: string; // default https://adhoc.leafbuyerloyalty.com/
  mode?: "pin" | "ageinput" | "ageselect"; // default pin
  pin?: string | null; // optional pre-set PIN
  default_template_id?: string | null;
}

interface LeafbuyerProviderParams extends ExternalProviderParams {
  data: LeafbuyerDataParams;
}

/**
 * Leafbuyer Text Provider
 * Uses OAuth client credentials to call Leafbuyer Ad Hoc Marketing GraphQL API
 * https://gitlab.com/leafbuyer-public/texting-api-3rd-party-documentation/-/blob/main/Leafbuyer%20Texting%20API%20-%20Getting%20Started.md
 */
export default class LeafbuyerTextProvider extends TextProvider {
  client_id!: string;
  client_secret!: string;
  auth_url?: string;
  audience?: string;
  adhoc_url?: string;
  mode?: "pin" | "ageinput" | "ageselect";
  pin?: string | null;
  default_template_id?: string | null;

  static namespace = "leafbuyer";
  static meta = {
    name: "Leafbuyer",
    description: "Leafbuyer SMS provider with age gate",
    url: "https://leafbuyer.com",
    icon: "https://beta.bakedbot.ai/images/providers/leafbuyer.svg",
  };

  static schema = ProviderSchema<LeafbuyerProviderParams, LeafbuyerDataParams>(
    "leafbuyerTextProviderParams",
    {
      type: "object",
      required: ["client_id", "client_secret"],
      properties: {
        client_id: { type: "string", title: "Client ID" },
        client_secret: { type: "string", title: "Client Secret" },
        auth_url: { type: "string", nullable: true },
        audience: { type: "string", nullable: true },
        adhoc_url: { type: "string", nullable: true },
        mode: {
          type: "string",
          enum: ["pin", "ageinput", "ageselect"],
          nullable: true,
        },
        pin: { type: "string", nullable: true },
        default_template_id: { type: "string", nullable: true },
      },
      additionalProperties: false,
    }
  );

  private tokenCache: { access_token: string; expires_at: number } | null = null;

  private getAuthUrl(): string {
    return this.auth_url || "https://leafbuyer.auth0.com/oauth/token";
  }

  private getAudience(): string {
    return this.audience || "api";
  }

  private getAdhocUrl(): string {
    return this.adhoc_url || "https://adhoc.leafbuyerloyalty.com/";
  }

  private async getAccessToken(): Promise<string> {
    const now = Math.floor(Date.now() / 1000);
    if (this.tokenCache && this.tokenCache.expires_at - 60 > now) {
      return this.tokenCache.access_token;
    }

    const form = new URLSearchParams();
    form.append("grant_type", "client_credentials");
    form.append("client_id", this.client_id);
    form.append("client_secret", this.client_secret);
    form.append("audience", this.getAudience());

    const res = await fetch(this.getAuthUrl(), {
      method: "POST",
      headers: { "content-type": "application/x-www-form-urlencoded" },
      body: form,
    });
    if (!res.ok) {
      const text = await res.text();
      throw new Error(`Leafbuyer OAuth failed: ${res.status} ${text}`);
    }
    const json: any = await res.json();
    const expires_in = typeof json.expires_in === "number" ? json.expires_in : 3600;
    this.tokenCache = {
      access_token: json.access_token,
      expires_at: now + expires_in,
    };
    return json.access_token;
  }

  // Prepare a Leafbuyer S3 location for campaign image upload and return signed URL + imageId
  private async prepareCampaignImageUpload(token: string): Promise<{ signedUploadUrl: string; imageId: string }> {
    const query = `mutation {\n  prepareCampaignImageUploadForSubjectLocation {\n    signedUploadUrl\n    imageId\n  }\n}`;
    const res = await fetch(this.getAdhocUrl(), {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        "User-Agent": "bakedbot/v1 (+https://github.com/dashon/smokey-ai)",
      },
      body: JSON.stringify({ query, variables: {} }),
    });

    const payload = await res.json().catch(() => ({} as any));
    const data = payload?.data?.prepareCampaignImageUploadForSubjectLocation;
    if (!res.ok || !data?.signedUploadUrl || !data?.imageId) {
      const errorMsg = payload?.errors?.[0]?.message || `Leafbuyer prepare image upload failed: ${res.status}`;
      throw new Error(errorMsg);
    }
    return { signedUploadUrl: data.signedUploadUrl, imageId: data.imageId };
  }

  private async fetchImageBuffer(url: string): Promise<{ buffer: Buffer; contentType: string }> {
    const resp = await fetch(url);
    if (!resp.ok) {
      const text = await resp.text().catch(() => "");
      throw new Error(`Failed to fetch media_url: ${resp.status} ${text}`);
    }
    const contentType = resp.headers.get("content-type") || "image/jpeg";
    const arrayBuffer = await resp.arrayBuffer();
    return { buffer: Buffer.from(arrayBuffer), contentType };
  }

  private async uploadToSignedUrl(signedUrl: string, buffer: Buffer, contentType: string) {
    const put = await fetch(signedUrl, {
      method: "PUT",
      headers: { "Content-Type": contentType },
      // Cast to any to satisfy fetch BodyInit types in Node; Buffer works at runtime
      body: buffer as any,
    });
    if (!put.ok) {
      const text = await put.text().catch(() => "");
      throw new Error(`Failed to upload image to Leafbuyer S3: ${put.status} ${text}`);
    }
  }

  async send(message: TextMessage): Promise<TextResponse> {
    const { to } = message;

    // Load location for redirectUrl if not already provided
    let redirectUrl = message.redirectUrl;
    if (!redirectUrl) {
      const location = await Location.find(this.location_id);
      redirectUrl = (location as any)?.sms_redirect_url || (location as any)?.website || undefined;
    }
    const redirectGraphQL = redirectUrl ? `"${redirectUrl}"` : "null";

    const token = await this.getAccessToken();

    // Prefer per-message templateId if provided; else provider default; else free text
    const effectiveTemplateId = message.templateId || this.default_template_id || null;
    const useTemplate = !!effectiveTemplateId;

    const firstName = message.firstName ? `"${message.firstName}"` : "null";
    const lastName = message.lastName ? `"${message.lastName}"` : "null";
    const birthDate = message.birthDate ? `"${message.birthDate}"` : "null";

    // If media_url is present, upload to Leafbuyer S3 and include imageId for MMS
    let imageIdGraphQL = "null";
    if (message.media_url) {
      try {
        const { signedUploadUrl, imageId } = await this.prepareCampaignImageUpload(token);
        const { buffer, contentType } = await this.fetchImageBuffer(message.media_url);
        await this.uploadToSignedUrl(signedUploadUrl, buffer, contentType);
        imageIdGraphQL = `"${imageId}"`;
      } catch (err) {
        logger.error({ err }, "Leafbuyer MMS image upload failed");
        throw err;
      }
    }

    // Build GraphQL mutation
    const webhookUrl = `${App.main.env.baseUrl || "https://beta.bakedbot.ai"}/api/leafbuyer/webhooks/adhoc`;
    const mutation = `mutation {\n  sendAdHocMarketingMessage(input: {\n    phoneNumber: "${to}",\n    messageBody: ${JSON.stringify(message.text || "")},\n    imageId: ${imageIdGraphQL},\n    customMetadata: ${JSON.stringify(webhookUrl)},\n    firstName: ${firstName},\n    lastName: ${lastName},\n    birthDate: ${birthDate},\n    pin: ${this.pin ? `"${this.pin}"` : "null"},\n    mode: "${this.mode || "pin"}",\n    templateId: ${useTemplate ? `"${effectiveTemplateId}"` : "null"},\n    redirectUrl: ${redirectGraphQL}\n  }) { status, dispatchId }\n}`;

    logger.info("=== LEAFBUYER SEND ===");
    logger.info(`📱 To: ${to}`);
    logger.info(`🔐 Mode: ${this.mode || "pin"}${this.pin ? " (with PIN)" : ""}`);
    if (useTemplate) {
      logger.info(`🧩 Template ID: ${effectiveTemplateId}`);
    } else {
      logger.info(`📝 Message: ${(message.text || "").slice(0, 120)}`);
    }
    if (message.media_url) {
      logger.info(`🖼️ Media URL: ${message.media_url}`);
    }
    logger.info(`↪️ Redirect URL: ${redirectUrl}`);

    const res = await fetch(this.getAdhocUrl(), {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        "User-Agent": "bakedbot/v1 (+https://github.com/dashon/smokey-ai)",
      },
      body: JSON.stringify({ query: mutation, variables: {} }),
    });

    const payload = await res.json().catch(() => ({} as any));
    const data = payload?.data?.sendAdHocMarketingMessage;
    if (res.ok && data && data.status) {
      return {
        message,
        success: true,
        response: data.dispatchId || data.status,
      };
    }

    const errorMsg = payload?.errors?.[0]?.message || `Leafbuyer send failed: ${res.status}`;
    throw new Error(errorMsg);
  }

  // Leafbuyer does not post inbound SMS to us; implement minimal parser to satisfy interface
  parseInbound(_inbound: any): InboundTextMessage {
    return { to: "", from: "", text: "" };
  }

  loadSetup(app: App): ProviderSetupMeta[] {
    return [
      { name: "Adhoc URL", value: this.getAdhocUrl() },
      { name: "Auth URL", value: this.getAuthUrl() },
      { name: "Audience", value: this.getAudience() },
      { name: "Location Redirect URL", value: `${app.env.baseUrl || "https://beta.bakedbot.ai"}` },
    ];
  }

  static controllers(): ProviderControllers {
    const admin = createController("text", this);
    // No public inbound endpoints for Leafbuyer at this time
    return { admin } as ProviderControllers;
  }
}
