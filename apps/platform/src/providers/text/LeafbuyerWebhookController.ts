import Router from "@koa/router";
import App from "../../app";
import { logger } from "../../config/logger";
import crypto from "crypto";
import SmsHistory from "./SmsHistory";
import { updateSendState } from "../../campaigns/CampaignService";

/**
 * Public webhook endpoint for Leafbuyer Ad-Hoc Messaging Webhooks
 * Verifies HMAC SHA256 signature per spec and enqueues internal events.
 */
// koa-body with includeUnparsed=true sets ctx.request.body to parsed object
// and ctx.request.rawBody to the raw Buffer/string in ctx.request.body[Symbol.for('unparsedBody')]

const router = new Router({ prefix: "/leafbuyer/webhooks" });

function timingSafeEqual(a: string, b: string) {
  const ab = Buffer.from(a, "hex");
  const bb = Buffer.from(b, "hex");
  if (ab.length !== bb.length) return false;
  return crypto.timingSafeEqual(ab, bb);
}

function computeSignature(secret: string, timestamp: string, fullUrl: string, payload: string) {
  // Concatenate timestamp + webhook URI + payload; include trailing newline after payload if present
  // Koa bodyparser yields stringified JSON without trailing newline; Leafbuyer note mentions including it if present
  const data = `${timestamp}${fullUrl}${payload}`;
  const hmac = crypto.createHmac("sha256", secret);
  hmac.update(data, "utf8");
  return hmac.digest("hex");
}

router.post("/adhoc", async (ctx) => {
  const signature = ctx.get("x-leafbuyer-signature");
  const timestamp = ctx.get("x-leafbuyer-timestamp");
  if (!signature || !timestamp) {
    ctx.status = 400;
    ctx.body = { error: "Missing signature headers" };
    return;
  }

  // Validate timestamp window (5 minutes)
  const ts = parseInt(timestamp, 10);
  const now = Math.floor(Date.now() / 1000);
  if (Number.isFinite(ts)) {
    if (Math.abs(now - ts) > 300) {
      ctx.status = 400;
      ctx.body = { error: "Stale timestamp" };
      return;
    }
  }

  const secret = App.main.env.sms.leafbuyer?.clientSecret;
  if (!secret) {
    ctx.status = 500;
    ctx.body = { error: "Leafbuyer client secret not configured" };
    return;
  }

  // We need the exact URL (path + query) used to post webhook
  const fullUrl = `${ctx.protocol}://${ctx.host}${ctx.originalUrl}`;

  // We need the raw request body string, not re-stringified
  // With koa-body(includeUnparsed=true), the raw body is available under Symbol.for('unparsedBody')
  const unparsed = (ctx.request as any).body?.[Symbol.for('unparsedBody')];
  const rawBody = typeof unparsed === 'string' ? unparsed : (unparsed ? unparsed.toString() : JSON.stringify(ctx.request.body));
  const expected = computeSignature(secret, timestamp, fullUrl, rawBody);

  if (!timingSafeEqual(expected, signature)) {
    logger.warn({ expected, signature }, "Leafbuyer webhook signature mismatch");
    ctx.status = 400;
    ctx.body = { error: "Invalid signature" };
    return;
  }

  const event = ctx.request.body || {};
  const type = event.event_type as string;

  // Always ACK success to avoid retries; we process async
  ctx.status = 204;

  try {
    switch (type) {
      case "MESSAGE_STATUS": {
        logger.info(event, "leafbuyer:message_status");
        const dispatchId: string | undefined = event.dispatchId || event.dispatch_id;
        const status: string | undefined = event.status;
        const phone: string | undefined = event.phone || event.phoneNumber;

        if (dispatchId && status) {
          try {
            // Update sms_history by dispatch_id (preferred) or by phone+recent
            const updated = await SmsHistory.update(
              (qb) => qb.where("dispatch_id", dispatchId),
              { status }
            );

            let record: SmsHistory | undefined;
            if (!updated && phone) {
              // Fetch the most recent matching by phone
              record = await SmsHistory.first((qb) =>
                qb.where("phone", phone).orderBy("id", "desc")
              );
              if (record) {
                await SmsHistory.update((qb) => qb.where("id", record!.id), { status });
              }
            }

            // If we have the campaign/user from the history, update campaign_sends
            const campaignId = (record as any)?.campaign_id;
            const userId = (record as any)?.user_id;
            if (campaignId && userId) {
              const mapped = status.toLowerCase();
              const state = mapped === "delivered" ? "sent" : mapped === "failed" ? "failed" : undefined;
              if (state) {
                await updateSendState({ campaign: campaignId, user: userId, state });
              }
            }
          } catch (e) {
            logger.error({ e, event }, "leafbuyer:update_status_failed");
          }
        }
        break;
      }
      case "INCOMING_MESSAGE":
        logger.info(event, "leafbuyer:incoming_message");
        break;
      case "MESSAGE_LINK_CLICKED":
        logger.info(event, "leafbuyer:link_clicked");
        break;
      case "GATE_PASSED_THROUGH":
        logger.info(event, "leafbuyer:gate_passed");
        break;
      default:
        logger.info(event, "leafbuyer:unknown_event");
    }
  } catch (err) {
    logger.error({ err, event }, "Leafbuyer webhook processing error");
  }
});

export default router;
