import Model from "../../core/Model";

export default class SmsHistory extends Model {
  // Match existing MySQL table name
  static get tableName() { return "sms_history"; }
  location_id!: number;
  admin_id?: number | null;
  user_id!: number;
  campaign_id?: number | null;
  insight_id?: number | null;
  phone!: string;
  provider!: string; // e.g., 'leafbuyer'
  type!: "SMS" | "MMS"; // message type
  status?: string | null; // QUEUED, SENT, FAILED (from provider)
  dispatch_id?: string | null; // Leafbuyer dispatchId
  template_id?: number | null;
  message?: string | null;
  response?: any;
  sent_at!: Date | string;
}
