import Router from "@koa/router";
import { LocationState } from "../../auth/AuthMiddleware";
import { locationRoleMiddleware } from "../../locations/LocationService";
import { SearchSchema } from "../../core/searchParams";
import { extractQueryParams } from "../../utilities";
import SmsHistory from "./SmsHistory";

const router = new Router<LocationState>({ prefix: "/sms-history" });
router.use(locationRoleMiddleware("support"));

router.get("/", async (ctx) => {
  const searchSchema = SearchSchema("smsHistorySearch", {
    sort: "id",
    direction: "desc",
  });
  const params = extractQueryParams(ctx.query, searchSchema);

  const result = await SmsHistory.search(
    { ...params, fields: ["phone", "type", "status", "dispatch_id"] },
    (qb) => {
      qb.where("location_id", ctx.state.location.id);
      const campaignId = (params as any)?.filter?.campaign_id;
      if (campaignId != null) {
        qb.where("campaign_id", campaignId);
      }
      return qb;
    }
  );

  ctx.body = result;
});

export default router;
