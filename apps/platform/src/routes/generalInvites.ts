import Router from "@koa/router";
import nodemailer from "nodemailer";
import bodyParser from "koa-bodyparser";
const router = new Router({ prefix: "/general-invites" });
router.use(bodyParser());

router.post("/send", async (ctx) => {
  const { to_email, from_email } = ctx.request.body;

  if (!to_email || !from_email) {
    ctx.status = 400;
    ctx.body = { error: "Missing to_email or from_email" };
    return;
  }

  try {
    const transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: "<EMAIL>", // REPLACE with test Gmail
        pass: "gwlwsndujmghwxrs", // use Gmail App Password
      },
    });

    const mailOptions = {
      from: from_email,
      to: to_email,
      subject: "You've been invited!",
      text: `Hey, you've been invited by ${from_email} to try our platform!`,
    };

    await transporter.sendMail(mailOptions);

    ctx.body = { success: true, message: "Em<PERSON> sent successfully!" };
  } catch (error) {
    ctx.status = 500;
    ctx.body = { success: false, error: (error instanceof Error ? error.message : "An unknown error occurred") };
  }
});

export default router;
