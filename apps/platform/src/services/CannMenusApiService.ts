/* eslint-disable indent */
import { logger } from "../config/logger";
import fetch from "node-fetch";
import CannMenusApiCall from "../cannmenus/CannMenusApiCall";

interface CannMenusApiConfig {
  baseUrl: string;
  apiKey?: string;
  maxConcurrentRequests?: number;
  requestDelay?: number;
  retryAttempts?: number;
  retryDelay?: number;
}

interface QueuedRequest {
  request: () => Promise<any>;
  resolve: (value: any) => void;
  reject: (error: any) => void;
  id: string;
  priority: number;
}

interface CacheItem {
  data: any;
  timestamp: number;
  ttl: number;
}

interface ProductsQuery {
  states?: string[];
  retailers?: number[];
  brands?: number[];
  lat?: number;
  lng?: number;
  distance?: number;
  category?: string;
  subcategory?: string;
  brand_name?: string;
  product_name?: string;
  page?: number;
  limit?: number;
}

interface RetailersQuery {
  id?: number;
  name?: string;
  city?: string;
  state?: string;
  zipcode?: string;
  lat?: number;
  lng?: number;
  distance?: number;
  page?: number;
}

interface BrandsQuery {
  id?: number;
  name?: string;
  page?: number;
}

type RequestMeta = {
  endpoint: string;
  params?: any;
  locationId?: number;
};

export class CannMenusApiService {
  private config: CannMenusApiConfig;
  private requestQueue: QueuedRequest[] = [];
  private processingQueue = false;
  private activeRequests = 0;
  private cache: Map<string, CacheItem> = new Map();

  // Performance metrics
  private metrics = {
    totalRequests: 0,
    cacheHits: 0,
    cacheMisses: 0,
    averageResponseTime: 0,
    failedRequests: 0,
    lastResetTime: Date.now(),
  };

  // Cache TTL configurations (in milliseconds) - optimized based on data volatility
  private readonly cacheTtl = {
    products: 4 * 60 * 60 * 1000, // 4 hours for product data (more frequent updates)
    pricing: 1 * 60 * 60 * 1000, // 1 hour for pricing (highly volatile)
    retailers: 24 * 60 * 60 * 1000, // 24 hours for retailer data
    brands: 7 * 24 * 60 * 60 * 1000, // 7 days for brand data
    market_trends: 12 * 60 * 60 * 1000, // 12 hours for trends (more responsive)
    competitive_analysis: 2 * 60 * 60 * 1000, // 2 hours for competitive data
    regional_analysis: 6 * 60 * 60 * 1000, // 6 hours for regional data
  };

  constructor(config: CannMenusApiConfig) {
    this.config = {
      maxConcurrentRequests: 3,
      requestDelay: 100, // 100ms between requests
      retryAttempts: 3,
      retryDelay: 500,
      ...config,
    };

    logger.info("CannMenusApiService initialized", {
      baseUrl: this.config.baseUrl,
      maxConcurrentRequests: this.config.maxConcurrentRequests,
      requestDelay: this.config.requestDelay,
    });
  }

  /**
   * Get products with geographic filtering and category targeting
   */
  async getProducts(query: ProductsQuery): Promise<any> {
    const cacheKey = this.getCacheKey("products", query);
    const cachedResult = this.getCachedResult(cacheKey, "products");

    if (cachedResult) {
      this.metrics.cacheHits++;
      logger.info("Using cached products data", { cacheKey });
      return cachedResult;
    }
    this.metrics.cacheMisses++;

    // Enforce retailer ID presence for v2 products API
    if (!query.retailers || (Array.isArray(query.retailers) && query.retailers.length === 0)) {
      throw new Error(
        "Cann Menus v2 products API requires 'retailers' parameter (retailer IDs). Pass the current location's Cann Menus retailer ID."
      );
    }

    const url = new URL("/v2/products", this.config.baseUrl);

    // Add query parameters
    this.addQueryParams(url, query);

    logger.info("Queuing products API request", {
      url: url.toString(),
      query,
      cacheKey,
    });

    const result = await this.enqueueRequest(
      () => this.makeRequest(url),
      "products"
    );

    // Cache the result
    this.cacheResult(cacheKey, result, "products");

    return result;
  }

  /**
   * Get products by meta SKU
   */
  async getProductsByMetaSku(query: ProductsQuery): Promise<any> {
    const cacheKey = this.getCacheKey("products_meta", query);
    const cachedResult = this.getCachedResult(cacheKey, "products");

    if (cachedResult) {
      logger.info("Using cached products meta data", { cacheKey });
      return cachedResult;
    }

    // Enforce retailer ID presence for v2 products meta API
    if (!query.retailers || (Array.isArray(query.retailers) && query.retailers.length === 0)) {
      throw new Error(
        "Cann Menus v2 products/meta API requires 'retailers' parameter (retailer IDs). Pass the current location's Cann Menus retailer ID."
      );
    }

    const url = new URL("/v2/products/meta", this.config.baseUrl);
    this.addQueryParams(url, query);

    logger.info("Queuing products meta API request", {
      url: url.toString(),
      query,
      cacheKey,
    });

    const result = await this.enqueueRequest(
      () => this.makeRequest(url),
      "products"
    );
    this.cacheResult(cacheKey, result, "products");

    return result;
  }

  /**
   * Fetch ALL products for the given query by paging through Cann Menus v2 API
   * Returns a normalized object with a flat data array and pagination info when available.
   */
  async getAllProducts(
    query: ProductsQuery & { limit?: number; page?: number },
    meta?: { locationId?: number }
  ): Promise<{ data: any[]; pages?: number; total?: number; per_page?: number }> {
    // Enforce retailer ID presence for v2 products API
    if (!query.retailers || (Array.isArray(query.retailers) && query.retailers.length === 0)) {
      throw new Error("Cann Menus v2 products API requires 'retailers' parameter (retailer IDs).");
    }

    const all: any[] = [];
    const limit = Math.max(1, Math.min(query.limit ?? 100, 500));
    let page = query.page ?? 1;
    let pages: number | undefined;
    let total: number | undefined;

    for (let i = 0; i < 200; i++) { // hard cap to prevent runaway loops
      const url = new URL("/v2/products", this.config.baseUrl);
      const params = { ...query, page, limit };
      this.addQueryParams(url, params);
      const res = await this.enqueueRequest(
        () => this.makeRequest(url),
        "products",
        { endpoint: "/v2/products", params, locationId: meta?.locationId }
      );

      const rawList: any[] = Array.isArray(res)
        ? res
        : Array.isArray(res?.data)
          ? res.data
          : [];

      // Flatten CannMenus v2 structure: [{ retailer_id, sku, products: [...] }, ...]
      const flat: any[] = Array.isArray(rawList) && rawList.length > 0 && Array.isArray((rawList[0] as any)?.products)
        ? rawList.flatMap((entry: any) =>
            Array.isArray(entry?.products)
              ? entry.products.map((p: any) => ({
                  ...p,
                  retailer_id:
                    p?.retailer_id ?? entry?.retailer_id ?? (Array.isArray(query.retailers) ? String(query.retailers[0]) : undefined),
                  sku: p?.sku ?? p?.cann_sku_id ?? entry?.sku,
                  meta_sku: p?.meta_sku ?? p?.cann_sku_id ?? entry?.sku,
                }))
              : []
          )
        : rawList;

      all.push(...flat);

      // Try to extract pagination metadata when present (support multiple shapes)
      const currentPage = (res?.page ?? res?.current_page ?? res?.meta?.page ?? res?.pagination?.current_page) as number | undefined;
      const totalPages = (res?.pages ?? res?.total_pages ?? res?.meta?.pages ?? res?.pagination?.total_pages) as number | undefined;
      const totalCount = (res?.total ?? res?.total_records ?? res?.total_count ?? res?.meta?.total ?? res?.pagination?.total_records) as number | undefined;

      if (totalPages && currentPage) {
        pages = totalPages;
        total = totalCount;
        if (currentPage >= totalPages) break;
      } else {
        // Fallback: stop when received less than limit entries
        if (rawList.length < limit) break;
      }

      page += 1;
    }

    return { data: all, pages, total, per_page: limit };
  }

  /**
   * Fetch ALL meta products for the given query by paging through Cann Menus v2 API
   */
  async getAllProductsByMetaSku(
    query: ProductsQuery & { limit?: number; page?: number },
    meta?: { locationId?: number }
  ): Promise<{ data: any[]; pages?: number; total?: number; per_page?: number }> {
    if (!query.retailers || (Array.isArray(query.retailers) && query.retailers.length === 0)) {
      throw new Error("Cann Menus v2 products/meta API requires 'retailers' parameter (retailer IDs).");
    }

    const all: any[] = [];
    const limit = Math.max(1, Math.min(query.limit ?? 100, 500));
    let page = query.page ?? 1;
    let pages: number | undefined;
    let total: number | undefined;

    for (let i = 0; i < 200; i++) {
      const url = new URL("/v2/products/meta", this.config.baseUrl);
      const params = { ...query, page, limit };
      this.addQueryParams(url, params);
      const res = await this.enqueueRequest(
        () => this.makeRequest(url),
        "products",
        { endpoint: "/v2/products/meta", params, locationId: meta?.locationId }
      );

      const rawList: any[] = Array.isArray(res)
        ? res
        : Array.isArray(res?.data)
          ? res.data
          : [];

      const flat: any[] = Array.isArray(rawList) && rawList.length > 0 && Array.isArray((rawList[0] as any)?.products)
        ? rawList.flatMap((entry: any) =>
            Array.isArray(entry?.products)
              ? entry.products.map((p: any) => ({
                  ...p,
                  retailer_id:
                    p?.retailer_id ?? entry?.retailer_id ?? (Array.isArray(query.retailers) ? String(query.retailers[0]) : undefined),
                  sku: p?.sku ?? p?.cann_sku_id ?? entry?.sku,
                  meta_sku: p?.meta_sku ?? p?.cann_sku_id ?? entry?.sku,
                }))
              : []
          )
        : rawList;

      all.push(...flat);

      const currentPage = (res?.page ?? res?.current_page ?? res?.meta?.page ?? res?.pagination?.current_page) as number | undefined;
      const totalPages = (res?.pages ?? res?.total_pages ?? res?.meta?.pages ?? res?.pagination?.total_pages) as number | undefined;
      const totalCount = (res?.total ?? res?.total_records ?? res?.total_count ?? res?.meta?.total ?? res?.pagination?.total_records) as number | undefined;

      if (totalPages && currentPage) {
        pages = totalPages;
        total = totalCount;
        if (currentPage >= totalPages) break;
      } else {
        if (rawList.length < limit) break;
      }

      page += 1;
    }

    return { data: all, pages, total, per_page: limit };
  }

  /**
   * Search retailers/dispensaries
   */
  async getRetailers(query: RetailersQuery, meta?: { locationId?: number }): Promise<any> {
    const cacheKey = this.getCacheKey("retailers", query);
    const cachedResult = this.getCachedResult(cacheKey, "retailers");

    if (cachedResult) {
      logger.info("Using cached retailers data", { cacheKey });
      return cachedResult;
    }

    const url = new URL("/v1/retailers", this.config.baseUrl);
    this.addQueryParams(url, query);

    logger.info("Queuing retailers API request", {
      url: url.toString(),
      query,
      cacheKey,
    });

    const result = await this.enqueueRequest(
      () => this.makeRequest(url),
      "retailers",
      { endpoint: "/v1/retailers", params: query, locationId: meta?.locationId }
    );
    this.cacheResult(cacheKey, result, "retailers");

    return result;
  }

  /**
   * Search brands
   */
  async getBrands(query: BrandsQuery): Promise<any> {
    const cacheKey = this.getCacheKey("brands", query);
    const cachedResult = this.getCachedResult(cacheKey, "brands");

    if (cachedResult) {
      logger.info("Using cached brands data", { cacheKey });
      return cachedResult;
    }

    const url = new URL("/v1/brands", this.config.baseUrl);
    this.addQueryParams(url, query);

    logger.info("Queuing brands API request", {
      url: url.toString(),
      query,
      cacheKey,
    });

    const result = await this.enqueueRequest(
      () => this.makeRequest(url),
      "brands"
    );
    this.cacheResult(cacheKey, result, "brands");

    return result;
  }

  /**
   * Batch competitor analysis - optimized for multiple retailers
   */
  async getBatchCompetitorData(
    retailerIds: number[],
    options: {
      categories?: string[];
      lat?: number;
      lng?: number;
      distance?: number;
      limit?: number;
    } = {}
  ): Promise<any> {
    const {
      categories = ["Flower", "Edibles", "Vapes"],
      lat,
      lng,
      distance,
      limit = 100,
    } = options;

    logger.info("Starting batch competitor analysis", {
      retailerCount: retailerIds.length,
      categories,
      hasLocation: !!(lat && lng),
      distance,
      limit,
    });

    // Batch requests by category for efficiency
    const analysisPromises = categories.map(async (category) => {
      const query: ProductsQuery = {
        retailers: retailerIds,
        category,
        limit: Math.ceil(limit / categories.length), // Distribute limit across categories
      };

      // Add geographic filtering if coordinates provided
      if (lat && lng) {
        query.lat = lat;
        query.lng = lng;
        query.distance = distance || 25; // Default 25 mile radius
      }

      return {
        category,
        data: await this.getProducts(query),
      };
    });

    const results = await Promise.all(analysisPromises);

    logger.info("Batch competitor analysis completed", {
      categoriesProcessed: results.length,
      totalProducts: results.reduce(
        (sum, r) => sum + (r.data?.data?.length || 0),
        0
      ),
    });

    return {
      categories: results,
      metadata: {
        retailerIds,
        categoriesAnalyzed: categories,
        timestamp: new Date().toISOString(),
      },
    };
  }

  /**
   * Get market analysis for a specific geographic area
   */
  async getMarketAnalysis(
    lat: number,
    lng: number,
    options: {
      radius?: number;
      categories?: string[];
      limit?: number;
      excludeRetailers?: number[];
    } = {}
  ): Promise<any> {
    const {
      radius = 25,
      categories = ["Flower", "Edibles", "Vapes"],
      limit = 200,
      excludeRetailers = [],
    } = options;

    logger.info("Starting market analysis", {
      lat,
      lng,
      radius,
      categories,
      limit,
      excludeRetailers,
    });

    // First get nearby retailers
    const retailersResponse = await this.getRetailers({
      lat,
      lng,
      distance: radius,
      page: 1,
    });

    const nearbyRetailers =
      retailersResponse.data?.filter(
        (retailer: any) => !excludeRetailers.includes(retailer.id)
      ) || [];

    logger.info("Found nearby retailers", {
      count: nearbyRetailers.length,
      excluded: excludeRetailers.length,
    });

    if (nearbyRetailers.length === 0) {
      return {
        retailers: [],
        categories: [],
        metadata: {
          message: "No retailers found in the specified area",
          searchParameters: { lat, lng, radius },
        },
      };
    }

    // Get product data for these retailers
    const retailerIds = nearbyRetailers.map((r: any) => r.id);
    const marketData = await this.getBatchCompetitorData(retailerIds, {
      categories,
      lat,
      lng,
      distance: radius,
      limit,
    });

    return {
      retailers: nearbyRetailers,
      categories: marketData.categories,
      metadata: {
        ...marketData.metadata,
        searchParameters: { lat, lng, radius },
        retailerCount: nearbyRetailers.length,
      },
    };
  }

  /**
   * Enqueue a request for processing, with optional logging metadata
   */
  private async enqueueRequest<T>(
    request: () => Promise<T>,
    priority: string = "normal",
    meta?: RequestMeta
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const priorityLevel = this.getPriorityLevel(priority);

      const wrappedRequest = async () => {
        const start = Date.now();
        try {
          const result = await request();
          // Best-effort logging if meta provided
          if (meta) {
            const count = Array.isArray((result as any))
              ? (result as any[]).length
              : Array.isArray((result as any)?.data)
              ? ((result as any)?.data?.length as number)
              : undefined;

            const pagination = {
              current_page:
                (result as any)?.pagination?.current_page ??
                (result as any)?.current_page ??
                (result as any)?.page ?? null,
              total_pages:
                (result as any)?.pagination?.total_pages ??
                (result as any)?.total_pages ??
                (result as any)?.pages ?? null,
              total_records:
                (result as any)?.pagination?.total_records ??
                (result as any)?.total_records ??
                (result as any)?.total ??
                (result as any)?.meta?.total ?? null,
            } as { current_page: number | null; total_pages: number | null; total_records: number | null };

            const paramsWithResp = {
              ...(meta.params ?? {}),
              __resp: {
                pagination,
                data_count: count ?? null,
              },
            };

            try {
              await (CannMenusApiCall as any).insert({
                location_id: meta.locationId ?? null,
                endpoint: meta.endpoint,
                method: "GET",
                params: paramsWithResp,
                status: 200,
                success: true,
                response_count: count ?? null,
                duration_ms: Date.now() - start,
              });
            } catch (logErr) {
              logger.warn("Failed to log CannMenus API call (success)", {
                error: (logErr as any)?.message,
              });
            }
          }
          return result;
        } catch (err: any) {
          if (meta) {
            try {
              await (CannMenusApiCall as any).insert({
                location_id: meta.locationId ?? null,
                endpoint: meta.endpoint,
                method: "GET",
                params: meta.params ?? null,
                status: (err as any)?.statusCode ?? null,
                success: false,
                response_count: null,
                duration_ms: Date.now() - start,
                error: err?.message || String(err),
              });
            } catch (logErr) {
              logger.warn("Failed to log CannMenus API call (error)", {
                error: (logErr as any)?.message,
              });
            }
          }
          throw err;
        }
      };

      const queuedRequest: QueuedRequest = {
        request: wrappedRequest,
        resolve,
        reject,
        id: Math.random().toString(36).substr(2, 9),
        priority: priorityLevel,
      };

      // Insert request in priority order
      const insertIndex = this.requestQueue.findIndex(
        (req) => req.priority < priorityLevel
      );

      if (insertIndex === -1) {
        this.requestQueue.push(queuedRequest);
      } else {
        this.requestQueue.splice(insertIndex, 0, queuedRequest);
      }

      this.processQueue();
    });
  }

  /**
   * Process the request queue
   */
  private async processQueue(): Promise<void> {
    if (this.processingQueue || this.requestQueue.length === 0) {
      return;
    }

    if (this.activeRequests >= this.config.maxConcurrentRequests!) {
      return;
    }

    this.processingQueue = true;

    while (
      this.requestQueue.length > 0 &&
      this.activeRequests < this.config.maxConcurrentRequests!
    ) {
      const queuedRequest = this.requestQueue.shift()!;
      this.activeRequests++;

      // Process request with delay
      setTimeout(async () => {
        try {
          const result = await queuedRequest.request();
          queuedRequest.resolve(result);
        } catch (error) {
          queuedRequest.reject(error);
        } finally {
          this.activeRequests--;
          this.processQueue(); // Continue processing queue
        }
      }, this.config.requestDelay);
    }

    this.processingQueue = false;
  }

  /**
   * Make the actual HTTP request with retry logic and metrics tracking
   */
  private async makeRequest(url: URL): Promise<any> {
    const startTime = Date.now();
    this.metrics.totalRequests++;
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.config.retryAttempts!; attempt++) {
      try {
        logger.debug("Making API request", {
          url: url.toString(),
          attempt,
          maxAttempts: this.config.retryAttempts,
        });

        const headers: Record<string, string> = {
          Accept: "application/json",
          "User-Agent": "BakedBot/1.0",
        };

        if (this.config.apiKey) {
          const apiKey = this.config.apiKey.trim().replace(/^['"]|['"]$/g, "");
          if (apiKey) headers["X-Token"] = apiKey;
        }

        const response = await fetch(url.toString(), {
          method: "GET",
          headers,
          timeout: 30000, // 30 second timeout
        });

        if (!response.ok) {
          let bodyText = "";
          try {
            bodyText = await response.text();
          } catch (_) {}
          const err: any = new Error(
            `HTTP ${response.status}: ${response.statusText}${bodyText ? ` - ${bodyText}` : ""}`
          );
          err.statusCode = response.status;
          err.statusText = response.statusText;
          err.body = bodyText;
          throw err;
        }

        const data = await response.json();

        // Update metrics for successful request
        const responseTime = Date.now() - startTime;
        this.updateAverageResponseTime(responseTime);

        logger.debug("API request successful", {
          url: url.toString(),
          status: response.status,
          dataSize: JSON.stringify(data).length,
          responseTime,
          attempt,
        });

        return data;
      } catch (error: any) {
        lastError = error as Error;

        logger.warn("API request failed", {
          url: url.toString(),
          attempt,
          error: lastError.message,
          status: error?.statusCode,
          willRetry: attempt < this.config.retryAttempts!,
        });

        if (attempt < this.config.retryAttempts!) {
          await new Promise((resolve) =>
            setTimeout(resolve, this.config.retryDelay! * attempt)
          );
        }
      }
    }

    // Update metrics for failed request
    this.metrics.failedRequests++;

    logger.error("API request failed after all retries", {
      url: url.toString(),
      attempts: this.config.retryAttempts,
      lastError: lastError?.message,
    });

    throw lastError || new Error("Request failed after all retries");
  }

  /**
   * Update average response time with new measurement
   */
  private updateAverageResponseTime(newTime: number): void {
    const successfulRequests =
      this.metrics.totalRequests - this.metrics.failedRequests;
    if (successfulRequests === 1) {
      this.metrics.averageResponseTime = newTime;
    } else {
      // Calculate rolling average
      this.metrics.averageResponseTime =
        (this.metrics.averageResponseTime * (successfulRequests - 1) +
          newTime) /
        successfulRequests;
    }
  }

  /**
   * Add query parameters to URL
   */
  private addQueryParams(url: URL, params: any): void {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          // Append repeated keys for arrays: key=value1&key=value2
          value.forEach((v) => url.searchParams.append(key, v.toString()));
        } else {
          url.searchParams.append(key, value.toString());
        }
      }
    });
  }

  /**
   * Generate cache key for a request
   */
  private getCacheKey(operation: string, params: any): string {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((result, key) => {
        result[key] = params[key];
        return result;
      }, {} as any);

    return `cann_menus:${operation}:${JSON.stringify(sortedParams)}`;
  }

  /**
   * Get cached result if still valid
   */
  private getCachedResult(key: string, dataType: string): any | null {
    const cached = this.cache.get(key);
    const ttl =
      this.cacheTtl[dataType as keyof typeof this.cacheTtl] ||
      this.cacheTtl.products;

    if (cached && Date.now() - cached.timestamp < ttl) {
      return cached.data;
    }

    if (cached) {
      this.cache.delete(key); // Remove expired cache
    }

    return null;
  }

  /**
   * Cache a result
   */
  private cacheResult(key: string, data: any, dataType: string): void {
    const ttl =
      this.cacheTtl[dataType as keyof typeof this.cacheTtl] ||
      this.cacheTtl.products;

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });

    logger.debug("Cached result", {
      key,
      dataType,
      ttl,
      cacheSize: this.cache.size,
    });
  }

  /**
   * Get priority level for queue ordering
   */
  private getPriorityLevel(priority: string): number {
    switch (priority) {
      case "high":
      case "pricing":
        return 10;
      case "normal":
      case "products":
        return 5;
      case "low":
      case "brands":
      case "retailers":
        return 1;
      default:
        return 5;
    }
  }

  /**
   * Clear expired cache entries
   */
  public clearExpiredCache(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp >= item.ttl) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach((key) => this.cache.delete(key));

    if (expiredKeys.length > 0) {
      logger.info("Cleared expired cache entries", {
        expiredCount: expiredKeys.length,
        remainingCount: this.cache.size,
      });
    }
  }

  /**
   * Get comprehensive performance metrics
   */
  public getPerformanceMetrics(): any {
    const runtime = Date.now() - this.metrics.lastResetTime;
    const cacheHitRate =
      this.metrics.totalRequests > 0
        ? (this.metrics.cacheHits /
            (this.metrics.cacheHits + this.metrics.cacheMisses)) *
          100
        : 0;

    return {
      cache: {
        size: this.cache.size,
        hitRate: Math.round(cacheHitRate * 100) / 100,
        hits: this.metrics.cacheHits,
        misses: this.metrics.cacheMisses,
        entries: Array.from(this.cache.keys()).map((key) => ({
          key,
          age: Date.now() - this.cache.get(key)!.timestamp,
          ttl: this.cache.get(key)!.ttl,
        })),
      },
      requests: {
        total: this.metrics.totalRequests,
        failed: this.metrics.failedRequests,
        successRate:
          this.metrics.totalRequests > 0
            ? Math.round(
                ((this.metrics.totalRequests - this.metrics.failedRequests) /
                  this.metrics.totalRequests) *
                  10000
              ) / 100
            : 100,
        averageResponseTime: Math.round(this.metrics.averageResponseTime),
        queueSize: this.requestQueue.length,
        activeRequests: this.activeRequests,
      },
      uptime: {
        milliseconds: runtime,
        formatted: this.formatUptime(runtime),
      },
      recommendations: this.generateOptimizationRecommendations(cacheHitRate),
    };
  }

  /**
   * Reset performance metrics
   */
  public resetMetrics(): void {
    this.metrics = {
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      averageResponseTime: 0,
      failedRequests: 0,
      lastResetTime: Date.now(),
    };
    logger.info("CannMenusApiService metrics reset");
  }

  /**
   * Generate optimization recommendations based on performance
   */
  private generateOptimizationRecommendations(cacheHitRate: number): string[] {
    const recommendations: string[] = [];

    if (cacheHitRate < 50) {
      recommendations.push(
        "Consider increasing cache TTL values - low cache hit rate detected"
      );
    }

    if (this.requestQueue.length > 10) {
      recommendations.push(
        "High request queue detected - consider increasing maxConcurrentRequests"
      );
    }

    if (this.metrics.failedRequests / this.metrics.totalRequests > 0.05) {
      recommendations.push(
        "High failure rate detected - check API connectivity and error handling"
      );
    }

    if (this.metrics.averageResponseTime > 2000) {
      recommendations.push(
        "High response times detected - consider optimizing query parameters"
      );
    }

    if (this.cache.size > 1000) {
      recommendations.push(
        "Large cache size detected - consider more aggressive cache cleanup"
      );
    }

    return recommendations;
  }

  /**
   * Format uptime for human readability
   */
  private formatUptime(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ${hours % 24}h ${minutes % 60}m`;
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  }

  // Add a helper for logging outgoing requests
  private logApiRequest(endpoint: string, params: any) {
    logger.info({
      message: "[CannMenusApiService] Outgoing API request",
      endpoint,
      params,
      timestamp: new Date().toISOString(),
    });
  }

  // Add a helper for logging API responses
  private logApiResponse(endpoint: string, status: number, response: any) {
    logger.info({
      message: "[CannMenusApiService] API response received",
      endpoint,
      status,
      responseSample: Array.isArray(response) ? response.slice(0, 2) : response,
      timestamp: new Date().toISOString(),
    });
  }

  // Add a helper for logging API errors
  private logApiError(endpoint: string, error: any) {
    logger.error({
      message: "[CannMenusApiService] API request failed",
      endpoint,
      error: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString(),
    });
  }
}

// Export a singleton instance
export const cannMenusApiService = new CannMenusApiService({
  baseUrl: process.env.CANNMENUS_API_URL || "https://api.cannmenus.com",
  apiKey: process.env.CANNMENUS_API_KEY,
});
