import { logger } from "../config/logger";
import { Email } from "../providers/email/Email";
import App from "../app";
import sgMail from "@sendgrid/mail";

interface GeneralInvitationParams {
  fromEmail: string;
  toEmail: string;
}

export class GeneralInvitationService {
  /**
   * Send a general invitation email to a dispensary owner
   */
  static async sendGeneralInvitation(params: GeneralInvitationParams): Promise<boolean> {
    try {
      const { fromEmail, toEmail } = params;
      
      logger.info(`🚀 Starting general invitation email process to ${toEmail} from ${fromEmail}`);

      // Get an available email provider
      // We'll try to get a global email provider since this is a general invitation
      const emailProvider = await this.getEmailProvider();

      if (!emailProvider) {
        logger.warn(`❌ No email provider available for general invitations`);
        logger.warn(`💡 To fix this: Configure an email provider (SendGrid, Mailgun, etc.) in Location Settings → Providers`);
        return false;
      }

      logger.info(`✅ Found email provider: SendGrid (Environment)`);

      // Generate the invitation email
      const email = this.generateGeneralInvitationEmail({
        fromEmail,
        toEmail,
      });

      logger.info(`📧 Sending general invitation email to ${toEmail}...`);
      logger.info(`   Subject: ${email.subject}`);
      logger.info(`   From: ${email.from}`);

      // Send the email
      await emailProvider.send(email);

      logger.info(`✅ Successfully sent general invitation email to ${toEmail}`);
      return true;
    } catch (error) {
      logger.error(`❌ Failed to send general invitation email to ${params.toEmail}:`, error);
      if (error instanceof Error) {
        logger.error(`   Error message: ${error.message}`);
        logger.error(`   Stack trace: ${error.stack}`);
      }
      return false;
    }
  }

  /**
   * Get an available email provider for sending general invitations
   * Uses environment variables directly instead of database providers
   */
  private static async getEmailProvider() {
    try {
      // Check if SendGrid API key is configured in environment variables
      const sendgridApiKey = process.env.SENDGRID_API_KEY;

      if (sendgridApiKey && sendgridApiKey.startsWith('SG.')) {
        logger.info("🌍 Using SendGrid from environment variables for general invitations");

        // Configure SendGrid
        sgMail.setApiKey(sendgridApiKey);

        // Return a simple email sender object
        return {
          send: async (email: Email) => {
            logger.info(`📧 Sending email via SendGrid to: ${email.to}`);
            logger.info(`   Subject: ${email.subject}`);
            logger.info(`   From: ${email.from}`);

            const result = await sgMail.send({
              to: email.to,
              from: typeof email.from === 'string' ? email.from : email.from.address,
              subject: email.subject,
              html: email.html,
              text: email.text,
            });

            logger.info(`✅ SendGrid send SUCCESS: ${result[0]?.statusCode}`);
            return result;
          }
        };
      }

      logger.warn("❌ No email provider available for general invitations");
      logger.warn("💡 Configure SENDGRID_API_KEY environment variable");
      return null;
    } catch (error) {
      logger.error('Failed to load email provider for general invitations:', error);
      return null;
    }
  }

  /**
   * Generate the general invitation email content
   */
  private static generateGeneralInvitationEmail(params: GeneralInvitationParams): Email {
    const { fromEmail, toEmail } = params;
    
    const baseUrl = App.main.env.baseUrl || 'https://app.bakedbot.ai';
    const signupUrl = `${baseUrl}/auth/login`;
    
    const subject = `🚀 You've been invited to join BakedBot AI – Transform Your Cannabis Business`

    const htmlContent = this.generateHtmlTemplate({
      fromEmail,
      toEmail,
      signupUrl,
    });

    const textContent = this.generateTextTemplate({
      fromEmail,
      toEmail,
      signupUrl,
    });

    return {
      to: toEmail,
      from: '<EMAIL>', // Using verified sender address
      subject,
      html: htmlContent,
      text: textContent,
    };
  }

  /**
   * Generate HTML email template for general invitations
   */
  private static generateHtmlTemplate(params: {
    fromEmail: string;
    toEmail: string;
    signupUrl: string;
  }): string {
    const { fromEmail, signupUrl } = params;

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Join BakedBot AI</title>
        <style>
          .container { max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; }
          .content { padding: 40px 20px; }
          .button { background-color: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold; }
          .footer { text-align: center; color: #6b7280; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="content">
            <p>Hello!</p>

            <p>You've been personally invited by <strong>${fromEmail}</strong> to join BakedBot AI – the revolutionary platform that's transforming cannabis retail with artificial intelligence.</p>

            <p>🔥 <strong>Why this matters:</strong><br>
            You're getting exclusive access to cutting-edge AI tools designed specifically for cannabis businesses:
            <ul>
              <li><strong>Smokey (AI Budtender)</strong> - Intelligent customer service and product recommendations</li>
              <li><strong>Craig (Marketing Automation)</strong> - Smart campaigns that drive real results</li>
              <li><strong>Pops (Business Intelligence)</strong> - Data insights that boost your bottom line</li>
            </ul>
            </p>

            <p>🚀 <strong>What's Next?</strong><br>
            Join thousands of dispensary owners who are already using AI to:</p>
            <ul>
              <li>Increase sales with personalized customer experiences</li>
              <li>Automate marketing campaigns that actually convert</li>
              <li>Get actionable insights from your business data</li>
              <li>Stay ahead of the competition with cutting-edge technology</li>
            </ul>

            <p style="text-align: center; margin: 30px 0;">
              <a href="${signupUrl}" class="button">Join BakedBot AI Now</a>
            </p>

            <p><strong>Plus, as a special bonus:</strong> Your friend who invited you gets 5 FREE spy chats for helping grow our community!</p>

            <p>This is your chance to be part of the future of cannabis retail. Don't miss out!</p>
          </div>
          
          <div class="footer">
            <p>Welcome to the future of cannabis retail,<br>
            — The BakedBot AI Team<br>
            <a href="https://www.bakedbot.ai">www.bakedbot.ai</a></p>
          </div>
        </div>
      </body>
      </html>
    `.trim();
  }

  /**
   * Generate plain text email template for general invitations
   */
  private static generateTextTemplate(params: {
    fromEmail: string;
    toEmail: string;
    signupUrl: string;
  }): string {
    const { fromEmail, signupUrl } = params;

    return `
Hello!

You've been personally invited by ${fromEmail} to join BakedBot AI – the revolutionary platform that's transforming cannabis retail with artificial intelligence.

🔥 Why this matters:
You're getting exclusive access to cutting-edge AI tools designed specifically for cannabis businesses:

• Smokey (AI Budtender) - Intelligent customer service and product recommendations
• Craig (Marketing Automation) - Smart campaigns that drive real results  
• Pops (Business Intelligence) - Data insights that boost your bottom line

🚀 What's Next?
Join thousands of dispensary owners who are already using AI to:

• Increase sales with personalized customer experiences
• Automate marketing campaigns that actually convert
• Get actionable insights from your business data
• Stay ahead of the competition with cutting-edge technology

👉 Join BakedBot AI Now: ${signupUrl}

Plus, as a special bonus: Your friend who invited you gets 5 FREE spy chats for helping grow our community!

This is your chance to be part of the future of cannabis retail. Don't miss out!

Welcome to the future of cannabis retail,
— The BakedBot AI Team
www.bakedbot.ai
    `.trim();
  }
}
