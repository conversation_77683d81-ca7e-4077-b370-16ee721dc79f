import Router from "@koa/router";
import App from "../app"; // adjust path if needed
import { createMonthlySubscription } from "./subscriptionService";
import { PLANS } from "./plans";

export default function VectorController(_app: App) {
  const router = new Router({ prefix: "/vector" });

  router.post("/subscribe", async (ctx) => {
    const { planId, email, firstName, lastName, card, opaqueData } = ctx.request.body || {};
    if (!planId || !(planId in PLANS)) ctx.throw(400, "Invalid or missing planId");
    if (!email || !firstName || !lastName) ctx.throw(400, "Missing customer info");

    const subscriptionId = await createMonthlySubscription({
      planId, email, firstName, lastName, card, opaqueData,
    });

    ctx.status = 201;
    ctx.body = { subscriptionId, status: "active" };
  });

  return router;
}
