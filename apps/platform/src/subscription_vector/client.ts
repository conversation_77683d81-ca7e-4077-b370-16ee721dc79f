// apps/platform/src/payments/vector/client.ts

import axios from "axios";
import {
  ANET_API_LOGIN_ID,
  ANET_TRANSACTION_KEY,
  ANET_URL
} from "./config";

/**
 * Returns the merchantAuthentication block needed by Authorize.Net requests.
 */
export function anetAuth() {
  if (!ANET_API_LOGIN_ID || !ANET_TRANSACTION_KEY) {
    throw new Error("[Vector/ANet] API Login ID or Transaction Key missing");
  }
  return {
    name: ANET_API_LOGIN_ID,
    transactionKey: ANET_TRANSACTION_KEY
  };
}

/**
 * Posts JSON to Authorize.Net and normalizes errors.
 * Throws an Error if the resultCode !== "Ok" or if the request fails.
 */
export async function anetPost<T = any>(payload: unknown): Promise<T> {
  try {
    const { data } = await axios.post<T>(ANET_URL, payload, {
      headers: { "Content-Type": "application/json" },
      timeout: 20000
    });

    // Authorize.Net uses messages.resultCode: "Ok" for success
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const resultCode = (data as any)?.messages?.resultCode;
    if (resultCode !== "Ok") {
      const messageList = (data as any)?.messages?.message || [];
      const first = messageList[0] || {};
      const code = first.code || "E_UNKNOWN";
      const text = first.text || "Payment error";
      const error = new Error(`[Vector/ANet] ${code}: ${text}`);
      // Attach raw response for debugging (optional)
      // @ts-ignore
      error.anetResponse = data;
      throw error;
    }
    return data;
  } catch (err) {
    // Normalize Axios errors into a single message
    if (axios.isAxiosError(err)) {
      const status = err.response?.status;
      const body = err.response?.data;
      throw new Error(
        `[Vector/ANet] HTTP ${status ?? "ERR"}: ${JSON.stringify(body ?? err.message)}`
      );
    }
    throw err;
  }
}

/**
 * Voids a previous transaction using its reference ID.  This can only be
 * performed before the original transaction has settled.  On success the
 * returned object will include the new transaction ID and authorization
 * code along with the raw response.
 *
 * @param refTransId The reference transaction ID to void
 * @throws Error if the gateway returns a non-success response
 */
export async function voidTransaction(refTransId: string) {
  const payload = {
    createTransactionRequest: {
      merchantAuthentication: anetAuth(),
      transactionRequest: {
        transactionType: "voidTransaction",
        refTransId
      }
    }
  };
  const data: any = await anetPost(payload);
  const responseCode = data?.transactionResponse?.responseCode;
  if (responseCode !== "1") {
    const errors = data?.transactionResponse?.errors || [];
    const messages = data?.messages?.message || [];
    const firstError = errors[0] || messages[0] || {};
    const code = firstError.errorCode || firstError.code || "UNKNOWN";
    const text = firstError.errorText || firstError.text || "Payment error";
    const error: any = new Error(`[Vector/ANet] ${code}: ${text}`);
    error.anetResponse = data;
    throw error;
  }
  return {
    status: "voided",
    transId: data?.transactionResponse?.transId ?? refTransId,
    authCode: data?.transactionResponse?.authCode ?? null,
    raw: data
  };
}

/**
 * Issues a refund for a settled transaction.  The original transaction must
 * already be settled for this to succeed.  You must supply the reference
 * transaction ID, the refund amount, and the last four digits of the card
 * used.  On success the returned object includes the new transaction ID and
 * authorization code along with the raw response.
 *
 * @param refTransId The reference transaction ID to refund
 * @param amount The amount to refund (string representation)
 * @param last4 The last four digits of the card used in the original transaction
 * @throws Error if the gateway returns a non-success response
 */
export async function refundTransaction(refTransId: string, amount: string, last4: string) {
  const payload = {
    createTransactionRequest: {
      merchantAuthentication: anetAuth(),
      transactionRequest: {
        transactionType: "refundTransaction",
        amount,
        payment: {
          creditCard: {
            cardNumber: last4,
            expirationDate: "XXXX"
          }
        },
        refTransId
      }
    }
  };
  const data: any = await anetPost(payload);
  const responseCode = data?.transactionResponse?.responseCode;
  if (responseCode !== "1") {
    const errors = data?.transactionResponse?.errors || [];
    const messages = data?.messages?.message || [];
    const firstError = errors[0] || messages[0] || {};
    const code = firstError.errorCode || firstError.code || "UNKNOWN";
    const text = firstError.errorText || firstError.text || "Payment error";
    const error: any = new Error(`[Vector/ANet] ${code}: ${text}`);
    error.anetResponse = data;
    throw error;
  }
  return {
    status: "refunded",
    transId: data?.transactionResponse?.transId,
    authCode: data?.transactionResponse?.authCode ?? null,
    raw: data
  };
}
