// apps/platform/src/subscription_vector/config.ts

export const ANET_API_LOGIN_ID = process.env.AUTHORIZE_NET_API_LOGIN_ID as string;
export const ANET_TRANSACTION_KEY = process.env.AUTHORIZE_NET_TRANSACTION_KEY as string;
export const ANET_ENV = (process.env.AUTHORIZE_NET_ENV || "sandbox") as "sandbox" | "production";

// Select sandbox or production API URL
export const ANET_URL =
  ANET_ENV === "production"
    ? "https://api.authorize.net/xml/v1/request.api"
    : "https://apitest.authorize.net/xml/v1/request.api";

// Warn during development if keys are missing
if (!ANET_API_LOGIN_ID || !ANET_TRANSACTION_KEY) {
  console.warn("[Vector/ANet] Missing API Login ID or Transaction Key in environment");
}
