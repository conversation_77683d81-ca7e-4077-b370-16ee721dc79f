// apps/platform/src/subscription_vector/routes.ts
import Router from "@koa/router";
import { createMonthlySubscription, cancelOrVoid, issueRefund } from "./subscriptionService";
import { PLANS } from "./plans";

const router = new Router({ prefix: "/vector" });

/**
 * POST /vector/subscribe
 * Body: { planId, email, firstName, lastName, card?, opaqueData? }
 * Creates a new monthly subscription and returns the subscriptionId.
 */
router.post("/subscribe", async (ctx) => {
  try {
    const {
      planId,
      email,
      firstName,
      lastName,
      card,
      opaqueData,
    } = ctx.request.body;

    // Basic validation
    if (!planId || !(planId in PLANS)) {
      ctx.throw(400, "Invalid or missing planId");
    }
    if (!email || !firstName || !lastName) {
      ctx.throw(400, "Missing customer information");
    }

    // Create the subscription via the service
    const subscriptionId = await createMonthlySubscription({
      planId,
      email,
      firstName,
      lastName,
      card,
      opaqueData,
    });

    // Return the newly created subscription ID
    ctx.status = 201;
    ctx.body = {
      subscriptionId,
      status: "active",
    };
  } catch (err: any) {
    // Normalize errors
    ctx.status = err.status || 500;
    ctx.body = { error: err.message || "An error occurred" };
  }
});

// POST /vector/void — body: { transId }
router.post("/void", async (ctx) => {
  const { transId } = ctx.request.body;
  if (!transId) ctx.throw(400, "transId required");
  const result = await cancelOrVoid(transId);
  ctx.status = 200;
  ctx.body = result;
});
  
// POST /vector/refund — body: { transId, amount, last4 }
router.post("/refund", async (ctx) => {
  const { transId, amount, last4 } = ctx.request.body;
  if (!transId || !amount || !last4) ctx.throw(400, "Missing transId, amount or last4");
  const result = await issueRefund(transId, amount, last4);
  ctx.status = 200;
  ctx.body = result;
});

export default router;
