import { anetAuth, anetPost, voidTransaction, refundTransaction } from "./client";
import { PLANS } from "./plans";

export async function cancelOrVoid(transId: string) {
  return await voidTransaction(transId);
}
  
export async function issueRefund(transId: string, amount: string, last4: string) {
  return await refundTransaction(transId, amount, last4);
}

export interface SubscribeParams {
  planId: keyof typeof PLANS;
  email: string;
  firstName: string;
  lastName: string;
  card?: {
    number: string;
    expYear: number;
    expMonth: number;
    cvv?: string;
  };
  opaqueData?: {
    dataDescriptor: string;
    dataValue: string;
  };
}

/**
 * Creates a monthly subscription in Authorize.Net (Vector).
 * Returns the resulting subscription ID.
 */
export async function createMonthlySubscription(params: SubscribeParams): Promise<string> {
  const { planId, email, firstName, lastName, card, opaqueData } = params;

  // Validate the planId
  const plan = PLANS[planId];
  if (!plan) {
    throw new Error(`Invalid planId: ${planId}`);
  }

  // Build the payment block:
  let payment: any;
  if (opaqueData) {
    payment = { opaqueData };
  } else if (card) {
    const exp = `${String(card.expYear).padStart(4, "0")}-${String(card.expMonth).padStart(2, "0")}`;
    payment = {
      creditCard: {
        cardNumber: card.number,
        expirationDate: exp,
        cardCode: card.cvv || ""
      }
    };
  } else {
    throw new Error("Either card or opaqueData must be provided");
  }

  // Build the ARB payload
  const payload = {
    ARBCreateSubscriptionRequest: {
      merchantAuthentication: anetAuth(),
      subscription: {
        name: plan.name,
        paymentSchedule: {
          interval: { length: 1, unit: "months" },
          startDate: new Date().toISOString().split("T")[0],
          totalOccurrences: 9999
        },
        amount: plan.price.toFixed(2),
        payment,
        billTo: { firstName, lastName },
        customer: { email }
      }
    }
  };

  // Call Authorize.Net via the helper
  const response = await anetPost<{ subscriptionId: string }>(payload);
  return response.subscriptionId;
}
