import { <PERSON><PERSON> } from "knex";
import App from "../app";
import { logger } from "../config/logger";

export interface UsageStatus {
  location_id: number;
  used: number;
  remaining: number;
  limit: number;
  exempt: boolean;
}

/**
 * AIUsageLimiter
 * - Tracks and enforces per-location AI usage limits for free tier.
 * - Uses an in-memory fallback with periodic DB sync to minimize overhead.
 * - Limit and exempt emails come from env.
 */
export class AIUsageLimiter {
  private static instance: AIUsageLimiter;
  private cache = new Map<number, { used: number; updatedAt: number }>();
  private TTL_MS = 60_000; // 1 minute cache TTL

  static getInstance() {
    if (!this.instance) this.instance = new AIUsageLimiter();
    return this.instance;
  }

  private get db(): Knex {
    return App.main.db;
  }

  private get limit(): number {
    return App.main.env.aiUsage.limit ?? 5;
  }

  /**
     * Ensures an organization has a trial state if none is set.
     * If neither artificial_trial_end nor a stripe_subscriptions row exists,
     * automatically sets artificial_trial_end to now + DEFAULT_TRIAL_DAYS.
     */
  private async ensureTrialState(organization_id: number, admin_email?: string): Promise<void> {
    try {
      const orgRow = await this.db("organizations")
        .where({ id: organization_id })
        .first();

      const stripe = await this.db("stripe_subscriptions")
        .where({ organization_id })
        .first("status");

      const hasOrg = !!orgRow;
      const hasOrgTrial = !!orgRow?.artificial_trial_end;
      const hasStripeRow = !!stripe;

      const days = parseInt(process.env.FREE_TIER_TRIAL_DAYS || "14", 10);
      const end = new Date();
      end.setDate(end.getDate() + (Number.isFinite(days) ? days : 14));

      if (!hasOrg) {
        // Create missing organization row with the specified id
        const username = admin_email || `org-${organization_id}`;
        const domain = admin_email || undefined;
        await this.db("organizations").insert({
          id: organization_id,
          username,
          domain,
          artificial_trial_end: end,
        });
        logger.info({ organization_id, artificial_trial_end: end }, "Created missing organization with default trial");
        return;
      }

      if (!hasOrgTrial && !hasStripeRow) {
        await this.db("organizations")
          .where({ id: organization_id })
          .update({ artificial_trial_end: end });
        logger.info({ organization_id, artificial_trial_end: end }, "Initialized default trial period for organization");
      }
    } catch (e) {
      logger.warn({ organization_id, error: e instanceof Error ? e.message : String(e) }, "Failed to ensure trial state (non-fatal)");
    }
  }

  private async loadUsed(location_id: number): Promise<number> {
    const row = await this.db("location_ai_usage").where({ location_id }).first();
    return row?.used ?? 0;
  }

  private async persist(location_id: number, used: number) {
    const existed = await this.db("location_ai_usage")
      .where({ location_id })
      .first("id");
    if (existed) {
      await this.db("location_ai_usage").where({ location_id }).update({ used });
    } else {
      await this.db("location_ai_usage").insert({ location_id, used });
    }
  }

  private isExemptEmail(email?: string): boolean {
    const normalized = (email || "").trim().toLowerCase();
    if (!normalized) return false;
    const superUsers = (App.main.env.superUser?.emails || []).map((e) => (e || "").trim().toLowerCase());
    const isExempt = superUsers.includes(normalized);
    if (isExempt) {
      logger.info({ email: normalized }, "AIUsageLimiter: superuser exemption active; skipping decrement");
    } else {
      logger.debug({ email: normalized }, "AIUsageLimiter: not exempt");
    }
    return isExempt;
  }

  async getStatus(params: {
    location_id: number;
    organization_id: number;
  }): Promise<UsageStatus> {
    const { location_id, organization_id } = params;

    // Ensure org has a trial state; if none, auto-initialize a default trial window
    await this.ensureTrialState(organization_id);

    const now = Date.now();
    const cached = this.cache.get(location_id);
    let used: number;
    if (cached && now - cached.updatedAt < this.TTL_MS) {
      used = cached.used;
    } else {
      used = await this.loadUsed(location_id);
      this.cache.set(location_id, { used, updatedAt: now });
    }

    return {
      location_id,
      used,
      remaining: Math.max(0, this.limit - used),
      limit: this.limit,
      exempt: false,
    };
  }

  /**
   * Throws if quota exceeded; otherwise increments usage and returns updated status.
   */
  async checkAndConsume(params: {
    location_id: number;
    organization_id: number;
    admin_email?: string;
  }): Promise<UsageStatus> {
    const status = await this.getStatus(params);

    // If admin email is exempt, do not decrement or block. Return current numeric status.
    const isExempt = this.isExemptEmail(params.admin_email);
    if (isExempt) {
      return status;
    }

    if (status.remaining <= 0) {
      logger.warn({ location_id: params.location_id }, "AI usage limit reached for location");
      const err: any = new Error(
        `Free trial limit reached. You have used ${status.used} of ${status.limit} AI actions.`
      );
      err.status = 402; // Payment required / limit reached
      throw err;
    }

    // Increment usage atomically
    const used = status.used + 1;
    await this.persist(params.location_id, used);
    this.cache.set(params.location_id, { used, updatedAt: Date.now() });

    return {
      ...status,
      used,
      remaining: Math.max(0, this.limit - used),
    };
  }
}

export default AIUsageLimiter.getInstance();
