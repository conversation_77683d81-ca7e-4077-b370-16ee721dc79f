import Router from "@koa/router";
import { LocationState } from "../auth/AuthMiddleware";

import AIUsageLimiter from "./AIUsageLimiter";

const router = new Router<LocationState>({ prefix: "/usage" });

// Returns current AI usage status for the location
router.get("/ai", async (ctx) => {
  const status = await AIUsageLimiter.getStatus({
    location_id: ctx.state.location.id,
    organization_id: ctx.state.location.organization_id,
  });

  // Dev-only diagnostics to help trace exemption issues
  if (process.env.NODE_ENV !== "production") {
    try {
      const adminEmail = ctx.state.admin
        ? (await (await import("../auth/AdminRepository")).getAdmin(
            ctx.state.admin.id,
            ctx.state.location.organization_id
          ))?.email
        : undefined;
      const superUsers = (await import("../config/env")).default().superUser
        .emails;
      ctx.set("X-AI-Checked-Email", adminEmail || "");
      ctx.set("X-AI-SuperUsers", (superUsers || []).join(","));
    } catch (e) {
      // ignore
    }
  }

  ctx.body = status;
});

export default router;
