import { client } from "../api";

const generalInvites = {
  send: async (payload: { from_email: string; to_email: string }) => {
    console.log("📤 API: Sending general invitation request", payload);
    try {
      const response = await client.post<{ message: string }>("/misc/general-invites/send", payload);
      console.log("✅ API: General invitation response", response);
      return response.data;
    } catch (error) {
      console.error("❌ API: General invitation error", error);
      throw error;
    }
  },
};

export default generalInvites;
