// Use the root API Axios client
import { client } from "../api";

export default {
  ingestUrls: async (urls: string[], locationId?: number) => {
    const res = await client.post(`/admin/kb/ingest-urls`, { urls, location_id: locationId });
    return res.data;
  },
  search: async (q: string, topK = 5, locationId?: number) => {
    const res = await client.get(`/admin/kb/search`, { params: { q, topK, location_id: locationId } });
    return res.data;
  },
  history: async (limit = 50, offset = 0, locationId?: number, q?: string) => {
    const res = await client.get(`/admin/kb/history`, { params: { limit, offset, location_id: locationId, q } });
    return res.data;
  },
  check: async (url: string, locationId?: number) => {
    const res = await client.get(`/admin/kb/check`, { params: { url, location_id: locationId } });
    return res.data as { last: any | null };
  }
};

