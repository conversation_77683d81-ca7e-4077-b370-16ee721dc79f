import { useCallback, useContext } from "react";
import { CampaignContext, LocationContext } from "../../contexts";
import api from "../../api";
import Heading from "../../ui/Heading";
import { SearchTable, useSearchTableState } from "../../ui/SearchTable";
import { SmsHistory } from "../../types";

export default function SmsHistoryTable() {
  const [location] = useContext(LocationContext);
  const [campaign] = useContext(CampaignContext);
  const searchState = useSearchTableState<SmsHistory>(
    useCallback(
      async (params) =>
        await api.smsHistory.search(location.id, {
          ...params,
          filter: { ...(params.filter || {}), campaign_id: campaign.id },
        }),
      [location.id, campaign.id]
    )
  );

  return (
    <>
      <Heading title="SMS History (This Campaign)" size="h4" />
      <SearchTable
        {...searchState}
        columns={[
          { key: "sent_at", title: "Sent At", sortable: true },
          { key: "phone", title: "Phone" },
          { key: "type", title: "Type" },
          { key: "status", title: "Status" },
          { key: "dispatch_id", title: "Dispatch ID" },
        ]}
      />
    </>
  );
}

