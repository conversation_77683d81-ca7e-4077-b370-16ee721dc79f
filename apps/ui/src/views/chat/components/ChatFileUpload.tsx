import React, { useState, useCallback, useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { FaFilePdf, FaFileWord, FaFileExcel, FaFileImage, FaFileAlt, FaTimes, FaCheck, FaExclamationTriangle } from 'react-icons/fa';
import { uploadFilesToChat, UploadedFileResponse } from '../../chat/chatService';
import './FileUpload.css';

// Supported file types for upload
const ALLOWED_FILE_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'text/plain',
  'text/csv',
  'image/jpeg',
  'image/png',
  'image/gif'
];

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const MAX_FILES = 4;

export interface ChatFilePreview {
  id: string;
  name: string;
  type: string;
  size: number;
  file: File;
  preview?: string;
  loading?: boolean;
  progress?: number;
  error?: string;
  url?: string;
  success?: boolean;
  status?: string;
  message?: string;
  documentId?: string; // Added to track the uploaded document ID
}

interface ChatFileUploadProps {
  locationId: string;
  chatId?: string | null;
  showPreviews?: boolean;
  onFilesChange?: (files: ChatFilePreview[]) => void;
  onUploadComplete?: (
    uploadedFiles: Array<{ 
      id: string; 
      url: string; 
      name: string; 
      type: string; 
      size: number;
      status: string;
      message?: string;
    }>,
    errors?: Array<{ fileName: string; error: Error }>
  ) => void;
  onClearAll?: () => void;
  disabled?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export interface ChatFileUploadRef {
  clearFiles: () => void;
  uploadFiles: () => Promise<Array<{
    id: string;
    url: string;
    name: string;
    type: string;
    size: number;
    status: string;
    message: string;
  }> | undefined>;
  getFiles: () => ChatFilePreview[];
  click: () => void; // Add click method to the interface
}

export const ChatFileUpload = forwardRef<ChatFileUploadRef, ChatFileUploadProps>(({
  locationId,
  chatId = null,
  showPreviews = true,
  onFilesChange,
  onUploadComplete,
  onClearAll,
  disabled = false,
  className = '',
  style = {}
}, ref) => {
  const [files, setFiles] = useState<ChatFilePreview[]>([]);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const uploadInProgress = useRef<Set<string>>(new Set());

  // Function to validate file before upload
  const validateFile = useCallback((file: File): { valid: boolean; error?: string } => {
    if (files.length >= MAX_FILES) {
      return {
        valid: false,
        error: `Maximum of ${MAX_FILES} files allowed`
      };
    }

    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      return {
        valid: false,
        error: `File type not supported. Please upload one of: ${ALLOWED_FILE_TYPES.join(', ')}`
      };
    }

    if (file.size > MAX_FILE_SIZE) {
      return {
        valid: false,
        error: `File is too large. Maximum size is ${MAX_FILE_SIZE / (1024 * 1024)}MB`
      };
    }

    return { valid: true };
  }, [files.length]);

  // Handle file selection
  const handleFileChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    if (!event.target.files || event.target.files.length === 0) return;
    
    const newFiles: ChatFilePreview[] = [];
    const errors: string[] = [];
    
    Array.from(event.target.files).forEach((file) => {
      const validation = validateFile(file);
      
      if (!validation.valid) {
        errors.push(`${file.name}: ${validation.error}`);
        return;
      }
      
      const fileId = `file-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      let preview: string | undefined;
      
      // Create preview for images
      if (file.type.startsWith('image/')) {
        preview = URL.createObjectURL(file);
      }
      
      newFiles.push({
        id: fileId,
        name: file.name,
        type: file.type,
        size: file.size,
        file,
        preview,
        loading: false,
        progress: 0,
        success: false,
        status: 'pending'
      });
    });
    
    if (errors.length > 0) {
      // Show errors to user (you can use a toast or other notification system)
      console.error('File upload errors:', errors);
    }
    
    if (newFiles.length > 0) {
      setFiles(prevFiles => {
        const updatedFiles = [...prevFiles, ...newFiles];
        onFilesChange?.(updatedFiles);
        return updatedFiles;
      });
    }
    
    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [validateFile, onFilesChange]);

  // Update a single file's state
  const updateFile = useCallback((id: string, updates: Partial<ChatFilePreview>) => {
    setFiles(prevFiles => {
      const updatedFiles = prevFiles.map(file => 
        file.id === id ? { ...file, ...updates } : file
      );
      onFilesChange?.(updatedFiles);
      return updatedFiles;
    });
  }, [onFilesChange]);

  // Upload a single file to the server
  const uploadFile = useCallback(async (
    file: ChatFilePreview,
    onProgress: (progress: number) => void
  ): Promise<UploadedFileResponse> => {
    // Update file state to show upload starting
    updateFile(file.id, { 
      loading: true, 
      progress: 0,
      status: 'uploading',
      message: 'Starting upload...' 
    });
    
    // Validate file type and size
    const validation = validateFile(file.file);
    if (!validation.valid) {
      throw new Error(validation.error || 'Invalid file');
    }
    
    try {
      const result = await uploadFilesToChat({
        files: [file.file],
        locationId,
        chatId: chatId || null,
        onFileProgress: (_fileIndex: number, progress: number) => {
          updateFile(file.id, { 
            progress,
            message: progress < 100 ? `Uploading... ${progress}%` : 'Processing file...'
          });
          onProgress(progress);
        }
      });

      // Handle empty or invalid response
      if (!result || result.length === 0) {
        throw new Error('The server did not return any data. Please try again.');
      }
      
      const uploadResult = result[0];
      
      // Handle error status from server
      if (uploadResult.status === 'error') {
        throw new Error(uploadResult.message || 'Failed to process the file. Please try again.');
      }
      
      // Update file state based on the status
      const isProcessing = uploadResult.status === 'processing';
      updateFile(file.id, {
        loading: isProcessing,
        progress: isProcessing ? 90 : 100,
        status: uploadResult.status,
        message: isProcessing 
          ? 'File is being processed and will be available shortly' 
          : 'Upload complete',
        success: !isProcessing,
        error: undefined,
        documentId: uploadResult.id,
        url: uploadResult.url
      });

      return uploadResult;
    } catch (error: any) {
      let errorMessage = 'Upload failed';
      
      // Check for AI usage limit error or 402 status code
      if (error?.response?.status === 402 || 
          error?.response?.data?.code === 'AI_USAGE_LIMIT_REACHED' || 
          error?.response?.data?.error?.includes('AI_USAGE_LIMIT_REACHED') ||
          error?.message?.includes('AI_USAGE_LIMIT_REACHED')) {
        errorMessage = 'You\'ve reached your free trial limit. Please upgrade your plan to continue using AI features.';
      } else if (error?.response?.data?.message || error?.message) {
        errorMessage = error.response?.data?.message || error.message;
      }
      updateFile(file.id, {
        loading: false,
        error: errorMessage,
        status: 'error',
        message: errorMessage,
        success: false
      });
      throw error;
    }
  }, [updateFile, validateFile, locationId, chatId]);

  // Handle file upload
  const handleUpload = useCallback(async () => {
    if (files.length === 0) return;
    
    setFiles(prevFiles => 
      prevFiles.map(file => ({
        ...file,
        loading: true,
        progress: 0,
        error: undefined,
        success: false
      })));

    const uploadPromises = files.map(async (file, index) => {
      try {
        const response = await uploadFilesToChat({
          files: [file.file],
          locationId,
          chatId: chatId || undefined,
          onFileProgress: (fileIndex, progress) => {
            if (fileIndex === 0) { // Since we're uploading one file at a time
              setFiles(prevFiles => 
                prevFiles.map((f, i) => 
                  i === index 
                    ? { ...f, progress: Math.min(progress, 90) } // Cap at 90% until complete
                    : f
                )
              );
            }
          }
        });

        // Get the first (and only) file from the response array
        const uploadedFile = Array.isArray(response) ? response[0] : response;
        if (!uploadedFile?.id) {
          throw new Error('Upload failed: Invalid response from server. Please try again.');
        }

        return {
          ...file,
          loading: false,
          progress: 100,
          success: true,
          status: uploadedFile.status || 'uploaded',
          message: uploadedFile.message || 'File uploaded successfully',
          documentId: uploadedFile.id,
          url: uploadedFile.url || ''
        };
      } catch (error: any) {
        console.error('Error uploading file:', error);
        return {
          ...file,
          loading: false,
          error: error?.message || 'Failed to upload file',
          success: false,
          status: 'error'
        };
      }
    });

    try {
      const results = await Promise.all(uploadPromises);
      
      setFiles(results);
      
      const successfulUploads = results
        .filter(file => file.success && file.documentId)
        .map(file => ({
          id: file.documentId || '',
          url: file.url || '',
          name: file.name,
          type: file.type,
          size: file.size,
          status: 'success',
          message: 'File uploaded successfully'
        }));
      
      const failedUploads = results
        .filter(file => !file.success)
        .map(file => ({
          fileName: file.name,
          error: new Error(file.error || 'Unknown error')
        }));
      
      onUploadComplete?.(successfulUploads, failedUploads.length > 0 ? failedUploads : undefined);
      
      if (successfulUploads.length === 0 && failedUploads.length > 0) {
        console.error('All file uploads failed');
      }
      
      return successfulUploads;
    } catch (error) {
      console.error('Error in file upload:', error);
      return [];
    }
  }, [files, locationId, chatId, onUploadComplete]);

  // Clear all files
  const clearFiles = useCallback(() => {
    setFiles([]);
    onFilesChange?.([]);
  }, [onFilesChange]);

  // Expose methods via ref
  useImperativeHandle(ref, () => ({
    clearFiles,
    uploadFiles: handleUpload,
    getFiles: () => [...files],
    click: () => {
      if (fileInputRef.current) {
        fileInputRef.current.click();
      }
    },
  }));

  // Auto-upload files when they're added
  useEffect(() => {
    if (files.length > 0) {
      handleUpload();
    }
  }, [files.length]); // eslint-disable-line react-hooks/exhaustive-deps

  // Clean up object URLs to avoid memory leaks
  useEffect(() => {
    return () => {
      files.forEach(file => {
        if (file.preview) {
          URL.revokeObjectURL(file.preview);
        }
      });
    };
  }, [files]);

  // Handle file removal
  const handleRemoveFile = useCallback((id: string) => {
    setFiles(prevFiles => {
      const fileToRemove = prevFiles.find(f => f.id === id);
      if (fileToRemove?.preview) {
        URL.revokeObjectURL(fileToRemove.preview);
      }
      const updatedFiles = prevFiles.filter(f => f.id !== id);
      onFilesChange?.(updatedFiles);
      return updatedFiles;
    });
  }, [onFilesChange]);

  // Get file icon based on file type
  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) return <FaFileImage className="file-icon" />;
    if (type.includes('pdf')) return <FaFilePdf className="file-icon" />;
    if (type.includes('word') || type.includes('document')) return <FaFileWord className="file-icon" />;
    if (type.includes('excel') || type.includes('spreadsheet')) return <FaFileExcel className="file-icon" />;
    return <FaFileAlt className="file-icon" />;
  };

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`chat-file-upload ${className}`} style={style}>
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        multiple
        accept={ALLOWED_FILE_TYPES.join(',')}
        disabled={disabled || files.length >= MAX_FILES || isUploading}
        style={{ display: 'none' }}
      />
      
      {/* <button
        type="button"
        className="upload-button"
        onClick={() => fileInputRef.current?.click()}
        disabled={disabled || files.length >= MAX_FILES || isUploading}
      >
        {isUploading ? 'Uploading...' : `Upload Files (${files.length}/${MAX_FILES})`}
      </button> */}
      
      {files.length > 0 && (
        <div className="file-previews">
          {files.map((file) => (
            <div 
              key={file.id} 
              className={`file-preview ${file.error ? 'error' : ''} ${file.success ? 'success' : ''}`}
            >
              <div className="file-preview-content">
                {showPreviews && file.preview && file.type.startsWith('image/') ? (
                  <img 
                    src={file.preview} 
                    alt={file.name} 
                    className="file-preview-image" 
                  />
                ) : (
                  <div className="file-icon-container">
                    {getFileIcon(file.type)}
                  </div>
                )}
                
                <div className="file-info">
                  <div className="file-name" title={file.name}>
                    {file.name}
                  </div>
                  <div className="file-meta">
                    {formatFileSize(file.size)}
                  </div>
                  
                  {file.loading && (
                    <div className="upload-progress">
                      <progress value={file.progress} max="100" />
                      <span>{file.progress}%</span>
                    </div>
                  )}
                  
                  {/* {file.error && (
                    <div className="error-message">
                      <FaExclamationTriangle /> 
                      {typeof file.error === 'string' && file.error.includes('AI_USAGE_LIMIT_REACHED') 
                        ? 'You\'ve reached your free trial limit. Please upgrade your plan to continue using AI features.'
                        : ""}
                    </div>
                  )} */}
                  
                  {file.success && (
                    <div className="success-message">
                      <FaCheck /> {file.message || 'Uploaded'}
                    </div>
                  )}
                </div>
              </div>
              
              <button
                type="button"
                className="remove-button"
                onClick={() => handleRemoveFile(file.id)}
                disabled={disabled || file.loading}
                title="Remove file"
              >
                <FaTimes />
              </button>
              
              {file.success && (
                <div className="success-indicator">
                  <FaCheck />
                </div>
              )}
            </div>
          ))}
        </div>
      )}
      
      {files.length > 0 && (
        <div className="file-upload-actions">
          <button
            type="button"
            className="clear-all-button"
            onClick={clearFiles}
            disabled={disabled || isUploading}
          >
            Clear All
          </button>
        </div>
      )}
    </div>
  );
});

// Export the component
export default ChatFileUpload;
