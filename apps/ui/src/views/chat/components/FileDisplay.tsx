import React from 'react';
import { FaFilePdf, FaFileWord, FaFileExcel, FaFileImage, FaFileAlt, FaDownload } from 'react-icons/fa';

interface FileDisplayProps {
  files: Array<{
    name: string;
    type: string;
    size: number;
    url: string;
  }>;
}

const getFileIcon = (type: string) => {
  if (type.includes('pdf')) return <FaFilePdf className="text-red-500" />;
  if (type.includes('word') || type.includes('document')) return <FaFileWord className="text-blue-500" />;
  if (type.includes('excel') || type.includes('spreadsheet')) return <FaFileExcel className="text-green-500" />;
  if (type.includes('image')) return <FaFileImage className="text-yellow-500" />;
  return <FaFileAlt className="text-gray-500" />;
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const FileDisplay: React.FC<FileDisplayProps> = ({ files }) => {
  if (!files || files.length === 0) return null;

  return (
    <div className="mt-2 space-y-2">
      {files.map((file, index) => (
        <div 
          key={index} 
          className="flex items-center p-2 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
        >
          <div className="flex-shrink-0 mr-3 text-xl">
            {getFileIcon(file.type)}
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              {file.name}
            </p>
            <p className="text-xs text-gray-500">
              {formatFileSize(file.size)}
            </p>
          </div>
          <a
            href={file.url}
            target="_blank"
            rel="noopener noreferrer"
            className="ml-2 p-2 text-gray-400 hover:text-gray-600"
            title="Download file"
          >
            <FaDownload className="text-sm" />
          </a>
        </div>
      ))}
    </div>
  );
};

export default FileDisplay;
