/* FileUpload.css */
.file-upload-container {
    margin: 1rem 0;
  }
  
  .upload-button {
    background-color: #4a90e2;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
  }
  
  .upload-button:hover {
    background-color: #357abd;
  }
  
  .upload-button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
  }
  
  .file-previews {
    margin-top: 0.5rem;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 0.5rem;
    width: 100%;
  }
  
  .file-preview {
    position: relative;
    display: flex;
    flex-direction: column;
    padding: 0.5rem;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background-color: #ffffff;
    transition: all 0.2s ease;
    height: 100%;
    box-sizing: border-box;
    min-height: 100px;
  }

  .file-preview:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
  
  .file-preview.error {
    border-color: #ff6b6b;
  }
  
  .file-preview-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    min-width: 0;
    text-align: center;
  }
  
  .file-preview-image {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
    margin-right: 1rem;
  }
  
  .file-icon-container {
    margin: 0.5rem 0 0.25rem;
    font-size: 1.5rem;
    color: #4a90e2;
    width: 100%;
    text-align: center;
  }
  
  .file-icon {
    font-size: 20px;
    color: #666;
  }
  
  .file-info {
    width: 100%;
    padding: 0.5rem 0;
  }
  
  .file-name {
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 0.5rem;
  }
  
  .file-meta {
    font-size: 11px;
    color: #777;
    margin-top: 0.1rem;
  }
  
  .upload-progress {
    font-size: 12px;
    color: #4a90e2;
  }
  
  .error-message {
    color: #ff6b6b;
    font-size: 12px;
    margin-top: 4px;
  }
  
  .remove-button {
    position: absolute;
    top: 2px;
    right: 2px;
    background: rgba(255, 255, 255, 0.8);
    border: none;
    border-radius: 50%;
    color: #999;
    cursor: pointer;
    padding: 0.25rem;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    transition: all 0.2s ease;
  }
  
  .remove-button:hover {
    background: #ff6b6b;
    color: white;
  }
  
  .remove-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }