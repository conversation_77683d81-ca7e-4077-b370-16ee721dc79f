import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { FaFilePdf, FaFileWord, FaFileExcel, FaFileImage, FaFileAlt, FaTimes } from 'react-icons/fa';
import { uploadFilesToChat, UploadedFileResponse } from '../../chat/chatService';
// Supported file types for upload
const ALLOWED_FILE_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'text/plain',
  'text/csv'
];

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
import './FileUpload.css';

export interface FilePreview {
  id: string;
  name: string;
  type: string;
  size: number;
  file: File;
  preview?: string;
  loading?: boolean;
  progress?: number;
  error?: string;
  url?: string;
  success?: boolean;
  status?: string;
  message?: string;
}

interface UploadError {
  fileName: string;
  error: Error;
}

interface FileUploadProps {
  files: FilePreview[];
  onFilesChange: (files: FilePreview[]) => void;
  locationId: string;
  chatId?: string | null;
  onUploadComplete?: (
    uploadedFiles: Array<{ 
      id: string; 
      url: string; 
      name: string; 
      type: string; 
      size: number;
      status: string;
      message?: string;
    }>,
    errors?: UploadError[]
  ) => void;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  files,
  onFilesChange,
  locationId,
  chatId = null,
  onUploadComplete,
}) => {
  // Function to validate file before upload
  const validateFile = useCallback((file: File): { valid: boolean; error?: string } => {
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      return {
        valid: false,
        error: `File type not supported. Please upload one of: ${ALLOWED_FILE_TYPES.join(', ')}`
      };
    }

    if (file.size > MAX_FILE_SIZE) {
      return {
        valid: false,
        error: `File is too large. Maximum size is ${MAX_FILE_SIZE / (1024 * 1024)}MB`
      };
    }

    return { valid: true };
  }, []);

  const [isUploading, setIsUploading] = useState<boolean>(false);
  const uploadInProgress = useRef<Set<string>>(new Set());
  const lastProcessedFiles = useRef<FilePreview[]>([]);

  // Update a single file's state
  const updateFile = useCallback((id: string, updates: Partial<FilePreview>) => {
    const updatedFiles = files.map(file => 
      file.id === id ? { ...file, ...updates } : file
    );
    onFilesChange(updatedFiles);
  }, [files, onFilesChange]);

  // Check if files have actually changed and are ready for upload
  const filesChanged = useMemo(() => {
    // If the number of files changed, check if we have new files to upload
    if (files.length !== lastProcessedFiles.current.length) {
      // Only return true if there are new files that need to be uploaded
      return files.some(f => !f.url && !f.loading && !f.error && !uploadInProgress.current.has(f.id));
    }
    
    // Check for any file that's newly added and ready for upload
    return files.some((file, index) => {
      const lastFile = lastProcessedFiles.current[index];
      const isNewFile = !lastFile || file.id !== lastFile.id;
      const needsUpload = !file.url && !file.loading && !file.error && !uploadInProgress.current.has(file.id);
      
      return isNewFile && needsUpload;
    });
  }, [files]);


   // Upload a single file to the server
   const uploadFile = useCallback(async (
    file: FilePreview,
    onProgress: (progress: number) => void
  ): Promise<UploadedFileResponse> => {
    const logPrefix = `[FileUpload:${file.name}`;
    
    // Update file state to show upload starting
    updateFile(file.id, { 
      loading: true, 
      progress: 0,
      status: 'uploading',
      message: 'Starting upload...' 
    });
    
    // Validate file type and size
    const validation = validateFile(file.file);
    if (!validation.valid) {
      throw new Error(validation.error || 'Invalid file');
    }
    
    try {
      console.log(`${logPrefix} Starting upload process`);
      
      // Validate file
      if (!file || !file.file) {
        const error = new Error('No file provided for upload');
        console.error(`${logPrefix} Validation failed - no file`);
        throw error;
      }

      console.log(`${logPrefix} Updating UI for upload start`);
      
      const uploadFileWithToken = async () => {
        console.log(`${logPrefix} Starting file upload`);
        
        try {
          const uploadResult = await uploadFilesToChat(
            {
              files: [file.file],
              locationId,
              chatId: chatId || null,
              onFileProgress: (_fileIndex: number, progress: number) => {
                console.log(`${logPrefix} Upload progress: ${progress}%`);
                updateFile(file.id, { 
                  progress,
                  message: progress < 100 ? `Uploading... ${progress}%` : 'Processing file...'
                });
                onProgress(progress);
              }
            }
          );

          // Handle empty or invalid response
          if (!uploadResult || uploadResult.length === 0) {
            throw new Error('The server did not return any data. Please try again.');
          }
          
          const result = uploadResult[0];
          
          // The server returns 200 with a status field for processing
          // Only throw if we get an explicit error status
          if (result.status === 'error') {
            throw new Error(result.message || 'Failed to process the file. Please try again.');
          }
          
          // For 'processing' status, we'll handle it as a success case
          // but with an indication that processing is ongoing

          // Update file state based on the status
          const isProcessing = result.status === 'processing';
          updateFile(file.id, {
            loading: isProcessing, // Keep loading if still processing
            progress: isProcessing ? 90 : 100, // Show 90% for processing, 100% for completed
            status: result.status,
            message: isProcessing 
              ? 'File is being processed and will be available shortly' 
              : 'Upload complete',
            success: !isProcessing, // Only mark as success if not processing
            error: undefined
          });

          console.log(`${logPrefix} Upload completed successfully`, result);
          return result;
        } catch (error: any) {
          console.error(`${logPrefix} Upload error:`, error);
          
          let errorMessage = 'An unexpected error occurred. Please try again.';
          
          // Handle specific error cases with friendly messages
          if (error?.response?.status === 401) {
            errorMessage = 'Session expired. Please refresh the page and try again.';
          } else if (error?.response?.status === 413) {
            errorMessage = 'File is too large. Maximum size is 10MB.';
          } else if (error?.response?.status === 415) {
            errorMessage = 'File type not supported. Please upload a PDF, Word, Excel, or CSV file.';
          } else if (error?.response?.status === 500) {
            errorMessage = 'Server error. Please try again later.';
          } else if (error?.message) {
            errorMessage = error.message;
          }
          
          // Update file state with error
          updateFile(file.id, {
            loading: false,
            error: errorMessage,
            status: 'error',
            message: errorMessage
          });
          
          throw new Error(errorMessage);
        }
      };

      const result = await uploadFileWithToken();

      console.log(`${logPrefix} Processing upload response`, {
        result,
        isProcessing: result.isProcessing
      });
      
      // Always show the local preview if available, otherwise use the server URL
      const previewUrl = file.preview || result.url || '';
      
      // Update file state with upload response
      const updatedFile = {
        loading: false, 
        progress: 100,
        success: result.status === 'completed',
        error: undefined,
        url: previewUrl, // Use local preview for processing files
        name: result.name || file.name,
        type: result.type || file.type,
        status: result.status || 'completed',
        message: result.status === 'processing' 
          ? 'File is being processed and will be available shortly' 
          : 'File uploaded successfully',
        size: result.size || file.size,
        // Keep the original file reference for processing state
        file: file.file,
        // Ensure we keep the preview URL
        preview: file.preview || result.preview
      };
      
      console.log(`${logPrefix} Updating file state:`, updatedFile);
      updateFile(file.id, updatedFile);

      return {
        id: result.id || file.id,
        url: result.url,
        preview: file.preview, // Include the local preview URL
        name: result.name || file.name,
        type: result.type || file.type,
        size: result.size || file.size,
        status: result.status || 'completed',
        message: result.message || 'File uploaded successfully',
        isProcessing: result.isProcessing || result.status === 'processing',
        file: file.file // Keep the original file reference
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      console.error(`${logPrefix} Upload failed:`, {
        error: errorMessage,
        stack: error instanceof Error ? error.stack : undefined
      });
      
      updateFile(file.id, { 
        loading: false, 
        error: errorMessage,
        success: false,
        progress: 0
      });
      
      throw error;
    }
  }, [updateFile, validateFile, locationId, chatId]);

  useEffect(() => {
    if (!filesChanged) return;
    
    const uploadAllFiles = async () => {
      console.log('[FileUpload] Starting upload process...');
      
      // Filter files that are ready for upload
      const filesToUpload = files.filter(f => 
        !f.url && 
        !f.loading && 
        !f.error && 
        !uploadInProgress.current.has(f.id)
      );
      
      console.log(`[FileUpload] ${filesToUpload.length} files ready for upload:`, 
        filesToUpload.map(f => f.name));
      
      if (filesToUpload.length === 0) {
        console.log('[FileUpload] No files to upload or already uploading');
        lastProcessedFiles.current = [...files];
        return;
      }
      
      // Mark files as in progress
      filesToUpload.forEach(f => uploadInProgress.current.add(f.id));
      setIsUploading(true);
      
      const uploadedFiles = [];
      const uploadErrors: UploadError[] = [];

      for (const file of filesToUpload) {
        console.log(`[FileUpload] Starting upload for: ${file.name}`);
        
        try {
          // Mark file as loading
          updateFile(file.id, { loading: true, progress: 0 });
          
          const uploadedFile = await uploadFile(file, (progress) => {
            console.log(`[FileUpload] Progress for ${file.name}: ${progress}%`);
            updateFile(file.id, { progress });
          });
          
          console.log(`[FileUpload] Successfully uploaded: ${file.name}`, uploadedFile);
          
          // Ensure we have a valid document ID from the response
          const documentId = uploadedFile.id || (uploadedFile as any).document_id;
          if (!documentId) {
            console.warn(`[FileUpload] No document ID found in upload response for ${file.name}`);
          }
          
          uploadedFiles.push({
            id: documentId || file.id, // Fall back to file.id if document_id is not available
            url: uploadedFile.url,
            name: file.name,
            type: file.type,
            size: file.size,
            status: uploadedFile.status || 'completed',
            message: uploadedFile.message || 'File uploaded successfully'
          });
          
          // Mark as completed
          updateFile(file.id, { 
            loading: false, 
            success: true, 
            url: uploadedFile.url,
            progress: 100 
          });
          
        } catch (error) {
          console.error(`[FileUpload] Failed to upload ${file.name}:`, error);
          const errorMessage = error instanceof Error ? error.message : 'Upload failed';
          updateFile(file.id, { 
            loading: false, 
            error: errorMessage,
            success: false 
          });
          
          uploadErrors.push({
            fileName: file.name,
            error: error instanceof Error ? error : new Error('Upload failed')
          });
        } finally {
          uploadInProgress.current.delete(file.id);
        }
      }

      console.log(`[FileUpload] Upload process completed. Success: ${uploadedFiles.length}, Failed: ${uploadErrors.length}`);
      setIsUploading(false);
      lastProcessedFiles.current = [...files];
      
      if (uploadedFiles.length > 0 || uploadErrors.length > 0) {
        onUploadComplete?.(uploadedFiles, uploadErrors.length > 0 ? uploadErrors : undefined);
      }
    };

    uploadAllFiles();
  }, [files, filesChanged, updateFile, uploadFile, onUploadComplete, locationId, chatId]);

 

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

//   const handleRemoveFile = useCallback((id: string) => {
//     // Find the file being removed
//     const fileToRemove = files.find(f => f.id === id);
    
//     // Revoke object URL if it exists to prevent memory leaks
//     if (fileToRemove?.preview && fileToRemove.preview.startsWith('blob:')) {
//       URL.revokeObjectURL(fileToRemove.preview);
//     }
    
//     // Remove from upload in progress set
//     uploadInProgress.current.delete(id);
    
//     // Update the parent's state to remove the file
//     onFilesChange(files.filter(f => f.id !== id));
    
//     console.log(`[FileUpload] Removed file with id: ${id}`);
//   }, [files, onFilesChange]);

const handleRemoveFile = useCallback((fileId: string) => {
    // Find the file being removed
    const fileToRemove = files.find(f => f.id === fileId);
    if (!fileToRemove) return;
    
    // Revoke object URL if it exists to prevent memory leaks
    if (fileToRemove.preview && fileToRemove.preview.startsWith('blob:')) {
      URL.revokeObjectURL(fileToRemove.preview);
    }
    
    // Remove from upload in progress set
    uploadInProgress.current.delete(fileId);
    
    // Update the parent's state to remove the file
    onFilesChange(files.filter(f => f.id !== fileId));
    
    console.log(`[FileUpload] Removed file from preview:`, { 
      id: fileId, 
      name: fileToRemove.name,
      type: fileToRemove.type
    });
  }, [files, onFilesChange]);

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) return <FaFileImage className="file-icon" />;
    if (fileType === 'application/pdf') return <FaFilePdf className="file-icon" />;
    if (fileType.includes('word')) return <FaFileWord className="file-icon" />;
    if (fileType.includes('excel')) return <FaFileExcel className="file-icon" />;
    return <FaFileAlt className="file-icon" />;
  };

  // Create preview URLs for image files
  useEffect(() => {
    files.forEach(file => {
      if (!file.preview && file.file) {
        if (file.file.type.startsWith('image/')) {
          const objectUrl = URL.createObjectURL(file.file);
          updateFile(file.id, { preview: objectUrl });
        } else {
          // For other file types, we'll just show the appropriate icon
          updateFile(file.id, { preview: undefined });
        }
      }
    });
    
    // Cleanup function to revoke object URLs when component unmounts
    return () => {
      files.forEach(file => {
        if (file.preview && file.preview.startsWith('blob:')) {
          URL.revokeObjectURL(file.preview);
        }
      });
    };
  }, [files, updateFile]);


  if (files.length === 0) return null;

  return (
    <div className="file-upload-container">
      <div className="file-previews">
        {files.map((file) => (
          <div key={file.id} className={`file-preview ${file.error ? 'error' : ''}`}>
            <div className="file-preview-content">
              {file.preview ? (
                <div className="file-preview-image-container">
                  <img 
                    src={file.preview} 
                    alt={file.name} 
                    className="file-preview-image" 
                    onLoad={() => {
                      // Revoke the object URL to avoid memory leaks
                      if (file.preview && !file.preview.startsWith('http')) {
                        URL.revokeObjectURL(file.preview);
                      }
                    }}
                  />
                </div>
              ) : (
                <div className="file-icon-container">
                  {getFileIcon(file.type)}
                </div>
              )}
              <div className="file-info">
                <div className="file-name" title={file.name}>
                  {file.name}
                </div>
                <div className="file-meta">
                  <span className="file-size">{formatFileSize(file.size)}</span>
                  {file.loading && (
                    <span className="upload-progress">
                      {file.progress}%
                    </span>
                  )}
                </div>
                {file.error && (
                  <div className="error-message">{file.error}</div>
                )}
              </div>
            </div>
            <button
              type="button"
              className="remove-button"
              onClick={() => handleRemoveFile(file.id)}
              disabled={isUploading}
              aria-label="Remove file"
            >
              <FaTimes />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};