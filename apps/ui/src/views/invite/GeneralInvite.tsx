import React from "react";
import useAuth from "../../hooks/useAuth";
import { Modal } from "../../ui";
import TextInput from "../../ui/form/TextInput";
import FormWrapper from "../../ui/form/FormWrapper";
import api from "../../api";
import { toast } from "react-hot-toast";

interface GeneralInviteProps {
  open: boolean;
  onClose: () => void;
}

export default function GeneralInvite({ open, onClose }: GeneralInviteProps) {
  const { user } = useAuth(); // ✅ Gets the logged-in user
  const [errorMessage, setErrorMessage] = React.useState<string | null>(null);
  const [successMessage, setSuccessMessage] = React.useState<string | null>(null);

  // Clear messages when modal opens
  React.useEffect(() => {
    if (open) {
      setErrorMessage(null);
      setSuccessMessage(null);
    }
  }, [open]);

  return (
    <Modal
      title="Invite Another Dispensary"
      open={open}
      onClose={onClose}
      description="Get 5 FREE spy chats by inviting a dispensary owner."
    >
      <FormWrapper<{ email: string }>
        onSubmit={async ({ email }) => {
          setErrorMessage(null);
          setSuccessMessage(null);

          try {
            console.log("🚀 Sending general invitation...", { from_email: user?.email ?? "", to_email: email });
            await api.generalinvites.send({
              from_email: user?.email ?? "",
              to_email: email,
            });
            console.log("✅ General invitation sent successfully");
            setSuccessMessage("✅ Invitation sent successfully!");
            toast.success("Invite sent successfully!");

            // Close modal after a short delay to show success message
            setTimeout(() => {
              onClose();
            }, 2000);
          } catch (e) {
            console.error("❌ General invitation failed:", e);
            setErrorMessage("❌ Failed to send invitation. Please check your email provider configuration.");
            toast.error("Failed to send invite.");
          }
        }}
        submitLabel="Send Invite"
      >
        {(form) => (
          <>
            {/* Success Message */}
            {successMessage && (
              <div style={{
                padding: '12px',
                marginBottom: '16px',
                backgroundColor: '#d4edda',
                color: '#155724',
                border: '1px solid #c3e6cb',
                borderRadius: '4px'
              }}>
                {successMessage}
              </div>
            )}

            {/* Error Message */}
            {errorMessage && (
              <div style={{
                padding: '12px',
                marginBottom: '16px',
                backgroundColor: '#f8d7da',
                color: '#721c24',
                border: '1px solid #f5c6cb',
                borderRadius: '4px'
              }}>
                {errorMessage}
              </div>
            )}

            <TextInput.Field
              form={form}
              name="email"
              label="Dispensary Owner Email"
              required
              placeholder="<EMAIL>"
            />
          </>
        )}
      </FormWrapper>
    </Modal>
  );
}
