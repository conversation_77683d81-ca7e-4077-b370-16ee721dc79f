import { useEffect, useMemo, useState } from "react";
import { client } from "../../api";
import Heading from "../../ui/Heading";
import { Button } from "../../ui";

interface AdminRetailer {
  retailer_id?: string | null;
  name?: string | null;
  dispensary_name?: string | null;
  city?: string | null;
  state?: string | null;
  [key: string]: any;
}

export default function CannMenusIntegration() {
  const [data, setData] = useState<AdminRetailer[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);

  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(25);
  const [search, setSearch] = useState("");
  const [missingOnly, setMissingOnly] = useState(true);
  const [sort, setSort] = useState("name");
  const [direction, setDirection] = useState<"asc" | "desc">("asc");

  const totalPages = useMemo(() => Math.max(1, Math.ceil(total / pageSize)), [total, pageSize]);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const res = await client.get("/retailers/admin", {
          params: {
            page,
            pageSize,
            search: search.trim(),
            missing_only: missingOnly,
            sort,
            direction,
          },
        });
        const payload = res?.data;
        const list = Array.isArray(payload)
          ? payload
          : Array.isArray(payload?.data)
            ? payload.data
            : Array.isArray(payload?.retailers)
              ? payload.retailers
              : [];
        setData(list);
        const totalCount = Number(payload?.total ?? 0);
        setTotal(Number.isFinite(totalCount) ? totalCount : 0);
      } catch (e) {
        console.error("Failed to load retailers (admin)", e);
        setData([]);
        setTotal(0);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [page, pageSize, search, missingOnly, sort, direction]);

  const toggleSort = (field: string) => {
    if (sort === field) {
      setDirection(direction === "asc" ? "desc" : "asc");
    } else {
      setSort(field);
      setDirection("asc");
    }
  };

  return (
    <div>
      <Heading size="h3" title="CannMenus Integration" />
      <p className="text-gray-600 mb-4">
        Super user maintenance view. Lists all retailers (dispensaries) and highlights rows without a Cann Menus retailer ID.
      </p>

      {/* Controls */}
      <div className="flex flex-col md:flex-row gap-3 items-start md:items-end mb-4">
        <div className="flex-1">
          <label htmlFor="retailerSearch" className="block text-sm text-gray-600 mb-1">Search</label>
          <input
            id="retailerSearch"
            type="text"
            value={search}
            onChange={(e) => { setPage(1); setSearch(e.target.value); }}
            placeholder="Search by name, city, state, or ID (case-insensitive)"
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        </div>
        <div className="flex items-center gap-2">
          <input
            id="missingOnly"
            type="checkbox"
            checked={missingOnly}
            onChange={(e) => { setPage(1); setMissingOnly(e.target.checked); }}
          />
          <label htmlFor="missingOnly" className="text-sm">Show missing Cann Menus ID only</label>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full border-collapse">
          <thead>
            <tr>
              <th className="py-2 px-4 text-left border border-gray-200 cursor-pointer" onClick={() => toggleSort("name")}>Name</th>
              <th className="py-2 px-4 text-left border border-gray-200 cursor-pointer" onClick={() => toggleSort("city")}>City</th>
              <th className="py-2 px-4 text-left border border-gray-200 cursor-pointer" onClick={() => toggleSort("state")}>State</th>
              <th className="py-2 px-4 text-left border border-gray-200 cursor-pointer" onClick={() => toggleSort("retailer_id")}>Cann Menus Retailer ID</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr><td colSpan={4} className="py-4 px-4 text-center text-gray-500">Loading...</td></tr>
            ) : data.length === 0 ? (
              <tr><td colSpan={4} className="py-4 px-4 text-center text-gray-500">No retailers found</td></tr>
            ) : (
              data.map((r, idx) => {
                const hasId = !!(r.retailer_id && String(r.retailer_id).trim());
                const displayName = r.name || r.dispensary_name || "(Unnamed)";
                return (
                  <tr key={idx} className="border-b" style={!hasId ? { backgroundColor: "#fff7ed" } : undefined}>
                    <td className="py-2 px-4 border border-gray-200">{displayName}</td>
                    <td className="py-2 px-4 border border-gray-200">{r.city || ""}</td>
                    <td className="py-2 px-4 border border-gray-200">{r.state || ""}</td>
                    <td className="py-2 px-4 border border-gray-200">
                      {hasId ? (
                        <span className="px-2 py-1 rounded text-xs bg-green-100 text-green-800">{r.retailer_id}</span>
                      ) : (
                        <span className="px-2 py-1 rounded text-xs bg-yellow-100 text-yellow-800">Missing</span>
                      )}
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between mt-4">
        <div className="flex items-center gap-2">
          <Button
            disabled={page <= 1 || loading}
            onClick={() => setPage((p) => Math.max(1, p - 1))}
          >Previous</Button>
          <span className="text-sm text-gray-600">Page {page} of {totalPages}</span>
          <Button
            disabled={page >= totalPages || loading}
            onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
          >Next</Button>
        </div>
        <div>
          <label className="text-sm mr-2">Rows per page</label>
          <select
            className="border border-gray-300 rounded px-2 py-1"
            value={pageSize}
            onChange={(e) => { setPage(1); setPageSize(parseInt(e.target.value, 10)); }}
          >
            {[10, 25, 50, 100].map((n) => (
              <option key={n} value={n}>{n}</option>
            ))}
          </select>
        </div>
      </div>
    </div>
  );
}

