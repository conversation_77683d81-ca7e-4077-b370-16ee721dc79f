import { useEffect, useState } from "react";
import PageContent from "../../ui/PageContent";
import Button from "../../ui/Button";
import Spinner from "../../ui/Spinner";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";

export default function Knowledgebase() {
  const { t } = useTranslation();
  const [kbUrls, setKbUrls] = useState<string>("");
  const [kbIngesting, setKbIngesting] = useState(false);
  const [kbResult, setKbResult] = useState<any | null>(null);
  const [kbHistory, setKbHistory] = useState<any[] | null>(null);
  const [kbHistoryLoading, setKbHistoryLoading] = useState(false);
  const [scope, setScope] = useState<"global" | "location" | null>(null);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(25);
  const [filter, setFilter] = useState("");
  const [checking, setChecking] = useState(false);
  const [urlStatus, setUrlStatus] = useState<Record<string, { last?: any }>>({});

  const { locationId: locationIdParam } = useParams();
  const locIdNum = locationIdParam ? parseInt(locationIdParam, 10) : undefined;

  const loadHistory = async (limit = pageSize, offset = page * pageSize) => {
    try {
      setKbHistoryLoading(true);
      const api = (await import("../../api/kb")).default;
      const hist = await api.history(limit, offset, locIdNum, filter || undefined);
      const rows = hist?.results || [];
      setScope(hist?.scope ?? null);
      setKbHistory(rows);
    } catch (e) {
      console.warn("Failed to load KB history", e);
    } finally {
      setKbHistoryLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    loadHistory();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Reload when paging/filter changes
  useEffect(() => {
    loadHistory();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, pageSize, filter]);

  const findLastProcessed = (url: string) => {
    if (!kbHistory) return null;
    const row = kbHistory.find((r) => r.url === url);
    return row || null;
  };

  // On-demand: check pasted URLs for last processed info
  const checkUrls = async () => {
    try {
      setChecking(true);
      const urls = kbUrls.split(/\r?\n/).map((u) => u.trim()).filter(Boolean);
      if (urls.length === 0) {
        setUrlStatus({});
        return;
      }
      const api = (await import("../../api/kb")).default;
      const status: Record<string, { last?: any }> = {};
      for (const u of urls) {
        const res = await api.check(u, locIdNum);
        status[u] = { last: res.last };
      }
      setUrlStatus(status);
    } catch (e) {
      console.warn("Failed to check URLs", e);
    } finally {
      setChecking(false);
    }
  };

  const handleIngest = async () => {
    try {
      setKbIngesting(true);
      setKbResult(null);
      const urls = kbUrls
        .split(/\r?\n/)
        .map((u) => u.trim())
        .filter(Boolean);
      if (urls.length === 0) return;

      // Per-URL confirmation if it exists in history
      const urlsToProcess: string[] = [];
      for (const u of urls) {
        const last = findLastProcessed(u);
        if (last) {
          const lastWhen = new Date(last.created_at).toLocaleString();
          const proceed = window.confirm(
            t(
              "kb_ingest_confirm_repeat",
              `This URL appears to have been processed before on ${lastWhen}. Proceed only if the page content has changed. Continue?`
            ) as string
          );
          if (!proceed) continue;
        }
        urlsToProcess.push(u);
      }
      if (urlsToProcess.length === 0) return;

      const api = (await import("../../api/kb")).default;
      const res = await api.ingestUrls(urlsToProcess, locIdNum);
      setKbResult(res);

      // Clear the input after a successful ingest
      setKbUrls("");
      setUrlStatus({});

      // Refresh history after ingest
      await loadHistory(50, 0);
      alert(t("kb_ingest_success", "Requested URLs processed. See details below."));
    } catch (err: any) {
      console.error("KB ingest error", err);
      alert(t("kb_ingest_failed", "Failed to ingest URLs. Check logs."));
    } finally {
      setKbIngesting(false);
    }
  };

  // Build a simple results view with status tags
  const renderResultPanel = () => {
    if (!kbResult) return null;
    const details: Array<{ url: string; ok: boolean; text_length?: number; error?: string; skipped?: boolean }>
      = kbResult.details || [];
    return (
      <div className="bg-gray-50 p-2 rounded text-xs overflow-auto max-h-60 mt-2">
        <div className="space-y-1">
          {details.map((d, i) => (
            <div key={i} className="flex items-center justify-between gap-2">
              <div className="truncate max-w-[32rem]" title={d.url}>{d.url}</div>
              <div className="flex items-center gap-2 whitespace-nowrap">
                {d.skipped ? (
                  <span className="px-2 py-0.5 rounded bg-gray-100 text-gray-700 border border-gray-200">
                    {t("skipped_no_change", "Skipped (no change)")}
                  </span>
                ) : d.ok ? (
                  <span className="px-2 py-0.5 rounded bg-green-100 text-green-700 border border-green-200">
                    {t("ingested", "Ingested")}
                  </span>
                ) : (
                  <span className="px-2 py-0.5 rounded bg-red-100 text-red-700 border border-red-200">
                    {t("error", "Error")}
                  </span>
                )}
                {typeof d.text_length === "number" && (
                  <span className="text-gray-500">{d.text_length} chars</span>
                )}
                {!d.ok && d.error && (
                  <span className="text-red-600 truncate max-w-[24rem]" title={d.error}>{d.error}</span>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Inline status hints based on "Check statuses"
  const renderInlineStatusHints = () => {
    const urls = kbUrls.split(/\r?\n/).map((u) => u.trim()).filter(Boolean);
    if (urls.length === 0) return null;
    return (
      <div className="mt-2 space-y-1 text-xs text-gray-600">
        {urls.slice(0, 10).map((u) => {
          const last = urlStatus[u]?.last;
          return (
            <div key={u} className="flex items-center gap-2">
              <span className="truncate max-w-[28rem]" title={u}>{u}</span>
              {last ? (
                <span className="px-2 py-0.5 rounded bg-yellow-50 text-yellow-700 border border-yellow-200">
                  {t("last_processed_at", "Last processed at")} {new Date(last.created_at).toLocaleString()}
                </span>
              ) : (
                <span className="px-2 py-0.5 rounded bg-green-50 text-green-700 border border-green-200">
                  {t("new_url", "New URL")}
                </span>
              )}
            </div>
          );
        })}
        {urls.length > 10 && (
          <div className="text-gray-500">+{urls.length - 10} {t("more", "more")}…</div>
        )}
      </div>
    );
  };

  return (
    <PageContent
      title={t("knowledgebase", "Knowledge Base")}
      desc={t(
        "train_smokey_desc",
        "Add public web pages to Smokey's knowledge base. Paste one or more URLs (one per line). We'll fetch the pages, extract cannabis-related text, and add it to the Global KB and, when applicable, the current Location KB. Searches always include Global results plus your location's KB, fused and ranked. Duplicates are automatically avoided."
      )}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Left: Ingest form */}
        <div>
          <textarea
            className="w-full border rounded p-2 h-48"
            placeholder={t("enter_urls_one_per_line", "Enter URLs, one per line") as string}
            value={kbUrls}
            onChange={(e) => setKbUrls(e.target.value)}
          />

          {renderInlineStatusHints()}
          {renderResultPanel()}

          <div className="flex justify-end gap-2 mt-2">
            <Button variant="secondary" onClick={checkUrls} disabled={checking || !kbUrls.trim()}>
              {checking ? <Spinner size="small" /> : t("check_statuses", "Check statuses")}
            </Button>
            <Button onClick={handleIngest} disabled={kbIngesting}>
              {kbIngesting ? <Spinner size="small" /> : t("ingest", "Ingest")}
            </Button>
          </div>
        </div>

        {/* Right: History list */}
        <div>
          <div className="flex items-center justify-between">
            <h3 className="font-medium flex items-center gap-2">
              {t("recent_ingests", "Recent ingests")}
              {scope === "global" && (
                <span className="px-2 py-0.5 rounded bg-blue-50 text-blue-700 border border-blue-200 text-xs">Global scope</span>
              )}
              {scope === "location" && (
                <span className="px-2 py-0.5 rounded bg-purple-50 text-purple-700 border border-purple-200 text-xs">Location scope</span>
              )}
            </h3>
            <div className="flex items-center gap-2">
              <input
                className="w-full border rounded p-2"
                placeholder={t("filter_by_url", "Filter by URL contains...") as string}
                value={filter}
                onChange={(e) => { setPage(0); setFilter(e.target.value); }}
              />
              <select
                className="border rounded p-1 text-xs"
                value={pageSize}
                onChange={(e) => {
                  setPageSize(parseInt(e.target.value, 10) || 25);
                  setPage(0);
                }}
              >
                {[10, 25, 50, 100].map((n) => (
                  <option key={n} value={n}>
                    {n}/page
                  </option>
                ))}
              </select>
              <Button size="small" variant="secondary" onClick={() => loadHistory()}>
                {t("refresh", "Refresh")}
              </Button>
            </div>
          </div>

          <div className="mt-2 border rounded divide-y">
            {kbHistoryLoading && (
              <div className="p-2 text-sm text-gray-500">{t("loading", "Loading...")}</div>
            )}
            {!kbHistoryLoading && (!kbHistory || kbHistory.length === 0) && (
              <div className="p-2 text-sm text-gray-500">{t("no_history", "No history yet.")}</div>
            )}
            {!kbHistoryLoading &&
              kbHistory &&
              kbHistory.map((row, idx) => (
                <div key={idx} className="p-2 text-sm flex items-center justify-between">
                  <div className="truncate">
                    <div className="font-medium truncate max-w-[32rem]" title={row.url}>
                      {row.url}
                    </div>
                    <div className="text-xs text-gray-500">
                      {(row.ok ? t("status_ok", "OK") : t("status_error", "Error")) as string} •
                      {(row.text_length || 0)} chars • {new Date(row.created_at).toLocaleString()} • {row.admin_email && row.admin_email !== 'unknown' ? row.admin_email : 'System'}
                    </div>
                  </div>
                  {!row.ok && row.error && (
                    <div className="text-xs text-red-600 ml-4 truncate max-w-[24rem]" title={row.error}>
                      {row.error}
                    </div>
                  )}
                </div>
              ))}
          </div>

          {!kbHistoryLoading && kbHistory && kbHistory.length > 0 && (
            <div className="flex justify-end items-center gap-2 p-2 text-xs text-gray-600">
              <Button
                size="tiny"
                variant="secondary"
                onClick={() => {
                  if (page > 0) setPage(page - 1);
                }}
              >
                {t("prev", "Prev")}
              </Button>
              <span>
                {t("page", "Page")} {page + 1}
              </span>
              <Button
                size="tiny"
                variant="secondary"
                onClick={() => {
                  setPage(page + 1);
                }}
              >
                {t("next", "Next")}
              </Button>
            </div>
          )}
        </div>
      </div>
    </PageContent>
  );
}

