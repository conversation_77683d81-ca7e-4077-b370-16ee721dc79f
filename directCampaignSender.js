// Direct Campaign Email Sender - Bypass platform job queue issues
import dotenv from 'dotenv';
dotenv.config();
import sgMail from '@sendgrid/mail';
import mysql from 'mysql2/promise';

// Set SendGrid API Key
sgMail.setApiKey(process.env.SENDGRID_API_KEY);

// Database connection
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: 'bakedbotpassword',
  database: 'bakedbot'
};

async function sendCampaignDirectly(campaignId) {
  console.log(`🚀 Starting direct campaign send for campaign ${campaignId}`);
  
  try {
    // Connect to database
    const connection = await mysql.createConnection(dbConfig);
    
    // Get campaign details
    const [campaigns] = await connection.execute(
      'SELECT * FROM campaigns WHERE id = ?',
      [campaignId]
    );
    
    if (campaigns.length === 0) {
      throw new Error(`Campaign ${campaignId} not found`);
    }
    
    const campaign = campaigns[0];
    console.log(`📧 Campaign: ${campaign.name}`);
    
    // Get template
    const [templates] = await connection.execute(
      'SELECT * FROM templates WHERE campaign_id = ? AND type = "email"',
      [campaignId]
    );
    
    if (templates.length === 0) {
      throw new Error(`No email template found for campaign ${campaignId}`);
    }
    
    const template = templates[0];
    const templateData = JSON.parse(template.data);
    console.log(`📝 Template: ${templateData.subject}`);
    
    // Get campaign sends (users to send to)
    const [sends] = await connection.execute(
      `SELECT cs.*, u.email, u.id as user_id 
       FROM campaign_sends cs 
       JOIN users u ON cs.user_id = u.id 
       WHERE cs.campaign_id = ? AND cs.state = 'pending'`,
      [campaignId]
    );
    
    console.log(`👥 Found ${sends.length} recipients`);
    
    let successCount = 0;
    let errorCount = 0;
    
    // Send emails directly
    for (const send of sends) {
      try {
        const emailMessage = {
          to: send.email,
          from: '<EMAIL>', // Use verified SendGrid sender
          subject: templateData.subject,
          text: templateData.text,
          html: templateData.html,
          // Add tracking headers like the platform does
          custom_args: {
            'X-Campaign-Id': campaignId.toString(),
            'X-User-Id': send.user_id.toString(),
            'X-Send-Id': send.id.toString()
          }
        };
        
        console.log(`📤 Sending to ${send.email}...`);
        const result = await sgMail.send(emailMessage);
        
        // Update campaign send status
        await connection.execute(
          'UPDATE campaign_sends SET state = "sent", sent_at = NOW() WHERE id = ?',
          [send.id]
        );
        
        successCount++;
        console.log(`✅ Sent to ${send.email} - Message ID: ${result[0].headers['x-message-id']}`);
        
        // Small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        errorCount++;
        console.error(`❌ Failed to send to ${send.email}:`, error.message);
        
        // Update campaign send status to failed
        await connection.execute(
          'UPDATE campaign_sends SET state = "failed" WHERE id = ?',
          [send.id]
        );
      }
    }
    
    // Update campaign status
    await connection.execute(
      'UPDATE campaigns SET state = "sent" WHERE id = ?',
      [campaignId]
    );
    
    await connection.end();
    
    console.log(`\n🎯 Campaign ${campaignId} completed:`);
    console.log(`✅ Successful sends: ${successCount}`);
    console.log(`❌ Failed sends: ${errorCount}`);
    console.log(`📊 Success rate: ${((successCount / (successCount + errorCount)) * 100).toFixed(1)}%`);
    
  } catch (error) {
    console.error('💥 Campaign send failed:', error.message);
    throw error;
  }
}

// Create a test campaign and send it
async function createAndSendTestCampaign() {
  console.log('🚀 Creating and sending test campaign...');

  try {
    const connection = await mysql.createConnection(dbConfig);

    // Get users from list 1 (which has 6 users)
    const [users] = await connection.execute(`
      SELECT u.id, u.email
      FROM users u
      JOIN user_list ul ON u.id = ul.user_id
      WHERE ul.list_id = 1 AND u.email IS NOT NULL
    `);

    console.log(`👥 Found ${users.length} users in list 1`);

    if (users.length === 0) {
      throw new Error('No users found in list 1');
    }

    // Create test campaign
    const [campaignResult] = await connection.execute(`
      INSERT INTO campaigns (
        type, location_id, list_ids, exclusion_list_ids, name, channel,
        provider_id, subscription_id, state, delivery, send_at,
        send_in_user_timezone, created_at, updated_at
      ) VALUES (
        'blast', 1, '[1]', '[]', 'Direct Test Campaign', 'email',
        2, 1, 'sent', '{"sent": 0, "opens": 0, "total": 0, "clicks": 0}',
        NOW(), 1, NOW(), NOW()
      )
    `);

    const campaignId = campaignResult.insertId;
    console.log(`📧 Created campaign ${campaignId}`);

    // Create email template
    const templateData = {
      subject: 'Test Email from Direct Campaign Sender',
      from: '<EMAIL>',
      text: 'This is a test email sent directly from the campaign sender script.',
      html: '<h1>Test Email</h1><p>This is a test email sent directly from the campaign sender script.</p><p>If you received this, the email system is working!</p>'
    };

    await connection.execute(`
      INSERT INTO templates (campaign_id, type, data, created_at, updated_at)
      VALUES (?, 'email', ?, NOW(), NOW())
    `, [campaignId, JSON.stringify(templateData)]);

    console.log('📝 Created email template');

    // Send emails to all users
    let successCount = 0;
    let errorCount = 0;

    for (const user of users) {
      try {
        // Create campaign send record
        const [sendResult] = await connection.execute(`
          INSERT INTO campaign_sends (campaign_id, user_id, state, created_at, updated_at)
          VALUES (?, ?, 'pending', NOW(), NOW())
        `, [campaignId, user.id]);

        const sendId = sendResult.insertId;

        const emailMessage = {
          to: user.email,
          from: '<EMAIL>',
          subject: templateData.subject,
          text: templateData.text,
          html: templateData.html,
          custom_args: {
            'X-Campaign-Id': campaignId.toString(),
            'X-User-Id': user.id.toString(),
            'X-Send-Id': sendId.toString()
          }
        };

        console.log(`📤 Sending to ${user.email}...`);
        const result = await sgMail.send(emailMessage);

        // Update campaign send status
        await connection.execute(
          'UPDATE campaign_sends SET state = "sent", sent_at = NOW() WHERE id = ?',
          [sendId]
        );

        successCount++;
        console.log(`✅ Sent to ${user.email} - Message ID: ${result[0].headers['x-message-id']}`);

        // Small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));

      } catch (error) {
        errorCount++;
        console.error(`❌ Failed to send to ${user.email}:`, error.message);
      }
    }

    await connection.end();

    console.log(`\n🎯 Test campaign completed:`);
    console.log(`✅ Successful sends: ${successCount}`);
    console.log(`❌ Failed sends: ${errorCount}`);
    console.log(`📊 Success rate: ${((successCount / (successCount + errorCount)) * 100).toFixed(1)}%`);

    if (successCount > 0) {
      console.log('\n🎉 EMAIL SYSTEM IS WORKING! Check your inbox for test emails.');
    }

  } catch (error) {
    console.error('💥 Test campaign failed:', error.message);
    throw error;
  }
}

// Command line usage
if (require.main === module) {
  const action = process.argv[2];

  if (action === 'test') {
    createAndSendTestCampaign()
      .then(() => {
        console.log('🎉 Test campaign completed successfully!');
        process.exit(0);
      })
      .catch((error) => {
        console.error('💥 Test campaign failed:', error.message);
        process.exit(1);
      });
  } else if (action && !isNaN(action)) {
    const campaignId = parseInt(action);
    sendCampaignDirectly(campaignId)
      .then(() => {
        console.log('🎉 Direct campaign send completed successfully!');
        process.exit(0);
      })
      .catch((error) => {
        console.error('💥 Direct campaign send failed:', error.message);
        process.exit(1);
      });
  } else {
    console.log('Usage:');
    console.log('  node directCampaignSender.js test                 # Create and send test campaign');
    console.log('  node directCampaignSender.js <campaign_id>        # Send existing campaign');
    console.log('Examples:');
    console.log('  node directCampaignSender.js test');
    console.log('  node directCampaignSender.js 42');
    process.exit(1);
  }
}

module.exports = { sendCampaignDirectly };
