version: '3.8'

services:
  api:
    build:
      context: .
      dockerfile: apps/platform/Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PYTHON_SERVICE_URL=http://document-processor-h7k0:8000
      - USE_PYTHON_SERVICE=true
      - PYTHON_SERVICE_POLL_INTERVAL_SECONDS=300
      # Add other production environment variables
    depends_on:
      - db
    networks:
      - app-network

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=bakedbot_prod
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=your_secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network

  # Note: document-processor will be deployed separately on Render
  # This is just for reference - remove this service in production
  # document-processor:
  #   # This service will be deployed separately on Render
  #   # URL: https://your-document-processor-service.onrender.com

volumes:
  postgres_data:

networks:
  app-network:
    driver: bridge
