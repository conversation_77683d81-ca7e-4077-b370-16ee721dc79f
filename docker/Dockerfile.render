# --------------> The frontend compiler image
FROM node:18 AS frontend_compile
WORKDIR /usr/src/app/apps/ui
COPY apps/ui/ ./

# Add these lines to create env file during build
ARG REACT_APP_FIREBASE_API_KEY
ARG REACT_APP_FIREBASE_AUTH_DOMAIN
ARG REACT_APP_FIREBASE_PROJECT_ID
ARG REACT_APP_FIREBASE_STORAGE_BUCKET
ARG REACT_APP_FIREBASE_MESSAGING_SENDER_ID
ARG REACT_APP_FIREBASE_APP_ID
ARG REACT_APP_FIREBASE_MEASUREMENT_ID
ARG REACT_APP_BASE_URL
ARG REACT_APP_STRIPE_PUBLISHABLE_KEY
ARG REACT_APP_STRIPE_PRICING_TABLE_ID
ARG REACT_APP_API_BASE_URL
ARG REACT_APP_PROXY_URL
ARG REACT_APP_AUTH_CALLBACK_URL
ARG REACT_APP_FACEBOOK_APP_ID
ARG REACT_APP_LINKEDIN_CLIENT_ID
ARG REACT_APP_SUBSCRIPTION_COMPLETED_URL
ARG SUPABASE_URL
ARG SUPABASE_KEY
ARG SUPABASE_BUCKET

RUN echo "REACT_APP_FIREBASE_API_KEY=$REACT_APP_FIREBASE_API_KEY" > .env && \
    echo "REACT_APP_FIREBASE_AUTH_DOMAIN=$REACT_APP_FIREBASE_AUTH_DOMAIN" >> .env && \
    echo "REACT_APP_FIREBASE_PROJECT_ID=$REACT_APP_FIREBASE_PROJECT_ID" >> .env && \
    echo "REACT_APP_FIREBASE_STORAGE_BUCKET=$REACT_APP_FIREBASE_STORAGE_BUCKET" >> .env && \
    echo "REACT_APP_FIREBASE_MESSAGING_SENDER_ID=$REACT_APP_FIREBASE_MESSAGING_SENDER_ID" >> .env && \
    echo "REACT_APP_FIREBASE_APP_ID=$REACT_APP_FIREBASE_APP_ID" >> .env && \
    echo "REACT_APP_FIREBASE_MEASUREMENT_ID=$REACT_APP_FIREBASE_MEASUREMENT_ID" >> .env && \
    echo "REACT_APP_BASE_URL=$REACT_APP_BASE_URL" >> .env && \
    echo "REACT_APP_STRIPE_PUBLISHABLE_KEY=$REACT_APP_STRIPE_PUBLISHABLE_KEY" >> .env && \
    echo "REACT_APP_STRIPE_PRICING_TABLE_ID=$REACT_APP_STRIPE_PRICING_TABLE_ID" >> .env && \
    echo "REACT_APP_API_BASE_URL=$REACT_APP_API_BASE_URL" >> .env && \
    echo "REACT_APP_PROXY_URL=$REACT_APP_PROXY_URL" >> .env && \
    echo "REACT_APP_AUTH_CALLBACK_URL=$REACT_APP_AUTH_CALLBACK_URL" >> .env && \
    echo "REACT_APP_FACEBOOK_APP_ID=$REACT_APP_FACEBOOK_APP_ID" >> .env && \
    echo "REACT_APP_LINKEDIN_CLIENT_ID=$REACT_APP_LINKEDIN_CLIENT_ID" >> .env && \
    echo "REACT_APP_SUBSCRIPTION_COMPLETED_URL=$REACT_APP_SUBSCRIPTION_COMPLETED_URL" >> .env && \
    echo "SUPABASE_URL=$SUPABASE_URL" >> .env && \
    echo "SUPABASE_KEY=$SUPABASE_KEY" >> .env && \
    echo "SUPABASE_BUCKET=$SUPABASE_BUCKET" >> .env

RUN npm ci
RUN npm run build

# --------------> The backend compiler image
FROM node:18 AS backend_compile
WORKDIR /usr/src/app/apps/platform
COPY apps/platform/ ./
RUN npm ci
RUN npm run build

# --------------> The build image
FROM node:18 AS build
WORKDIR /usr/src/app
COPY --from=backend_compile /usr/src/app/apps/platform/package*.json ./
COPY --from=backend_compile /usr/src/app/apps/platform/build ./build
COPY --from=backend_compile /usr/src/app/apps/platform/db ./db
COPY --from=backend_compile /usr/src/app/apps/platform/public ./public
COPY --from=frontend_compile /usr/src/app/apps/ui/build ./public
RUN npm ci --only=production

# --------------> The production image
FROM node:18-alpine
RUN apk add --no-cache dumb-init
ENV PORT=$PORT
ENV NODE_ENV="production"
# run as root so we can write to mounted Render Disk at /usr/src/app/public/uploads
USER root
WORKDIR /usr/src/app
COPY --chown=node:node --from=build /usr/src/app ./
# ensure uploads dir exists
RUN mkdir -p /usr/src/app/public/uploads
EXPOSE 80
CMD ["/bin/sh", "-lc", "chown -R node:node /usr/src/app/public/uploads || true; exec dumb-init node build/boot.js"]

