{"name": "@bakedbot/supabase-tools", "version": "0.1.0", "private": true, "description": "Helper scripts to manage Supabase migrations for local and remote environments", "license": "UNLICENSED", "scripts": {"start:local": "bash -lc 'cd ../.. && supabase start'", "stop:local": "bash -lc 'cd ../.. && supabase stop'", "migrate:local": "bash -lc 'cd ../.. && supabase db push'", "reset:local": "bash -lc 'cd ../.. && supabase db reset --force'", "link:remote": "bash -lc 'cd ../.. && : ${SUPABASE_PROJECT_REF:?SUPABASE_PROJECT_REF is required}; supabase link --project-ref \"$SUPABASE_PROJECT_REF\"'", "migrate:remote": "bash -lc 'cd ../.. && : ${SUPABASE_DB_URL:?SUPABASE_DB_URL is required}; supabase db push --db-url \"$SUPABASE_DB_URL\"'"}}