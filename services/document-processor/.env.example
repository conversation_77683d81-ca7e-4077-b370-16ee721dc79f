# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Pinecone Configuration
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=your_pinecone_environment_here
PINECONE_INDEX_NAME=cannabis-data

# Redis Configuration (for job queue)
REDIS_URL=redis://redis:6379/0

# File Processing Configuration
MAX_FILE_SIZE_MB=10
MAX_CHUNKS_PER_DOCUMENT=100
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# Logging Configuration
LOG_LEVEL=INFO

# Service Configuration
SERVICE_NAME=document-processor
SERVICE_VERSION=1.0.0

# Web Link Ingestion
ENABLE_WEB_LINK_INGEST=true
URL_FETCH_CONCURRENCY=4
URL_FETCH_TIMEOUT_SECONDS=10
URL_MAX_PER_DOCUMENT=10
URL_MAX_CONTENT_LENGTH=200000

RESPECT_ROBOTS_TXT=true
CRAWLER_USER_AGENT=BakedBot-DocProcessor/1.0
MAX_WEB_CHUNKS_PER_DOCUMENT=200


# Domain controls
ALLOWED_DOMAINS=
BLOCKED_DOMAINS=
