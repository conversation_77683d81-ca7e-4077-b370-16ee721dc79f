"""
API routes for document processing service
"""

import os
import time
import asyncio
from datetime import datetime
from typing import Dict, Any
from pathlib import Path
from fastapi import APIRouter, HTTPException, UploadFile, File, Form, BackgroundTasks, Request
from fastapi.responses import JSONResponse
from loguru import logger

from models.document import ProcessingRequest, ProcessingStatus, ProcessingResult, VectorizationResult
from config.settings import settings


router = APIRouter()

# In-memory status tracking (in production, use Redis or database)
processing_status: Dict[str, ProcessingStatus] = {}


@router.post("/process-document")
async def process_document_endpoint(
    background_tasks: BackgroundTasks,
    request: Request,
    document_id: str = Form(...),
    location_id: str = Form(...),
    callback_url: str = Form(None),
    file: UploadFile = File(...)
):
    """
    Process a document: extract text, create chunks, and vectorize
    """
    try:
        logger.info(f"🐍🐍🐍 PYTHON SERVICE - Received document processing request: {document_id}")
        logger.info(f"🐍🐍🐍 PYTHON SERVICE - Request details: document_id={document_id}, location_id={location_id}, callback_url={callback_url}, filename={file.filename}")

        # Validate file
        if not file.filename:
            raise HTTPException(status_code=400, detail="No file provided")

        # Check file extension
        file_extension = Path(file.filename).suffix.lower()
        if file_extension not in settings.ALLOWED_EXTENSIONS:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported file type: {file_extension}. Allowed: {settings.ALLOWED_EXTENSIONS}"
            )

        # Save uploaded file
        upload_path = Path(settings.UPLOAD_DIR) / f"{document_id}_{file.filename}"

        with open(upload_path, "wb") as buffer:
            content = await file.read()

            # Check file size
            if len(content) > settings.MAX_FILE_SIZE_MB * 1024 * 1024:
                raise HTTPException(
                    status_code=400,
                    detail=f"File too large: {len(content) / (1024*1024):.1f}MB > {settings.MAX_FILE_SIZE_MB}MB"
                )

            buffer.write(content)

        # Initialize processing status
        processing_status[document_id] = ProcessingStatus(
            document_id=document_id,
            location_id=location_id,
            status="pending",
            current_step="File uploaded, queuing for processing"
        )

        # Start background processing
        background_tasks.add_task(
            process_document_background,
            str(upload_path),
            document_id,
            location_id,
            callback_url,
            request.app.state.document_service,
            request.app.state.vector_service
        )

        return {
            "message": "Document processing started",
            "document_id": document_id,
            "status": "pending",
            "status_url": f"/api/v1/status/{document_id}"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"🔥🔥🔥 PYTHON SERVICE ERROR in process_document_endpoint: {e}")
        logger.error(f"🔥🔥🔥 PYTHON SERVICE ERROR details: {type(e).__name__}: {str(e)}")
        import traceback
        logger.error(f"🔥🔥🔥 PYTHON SERVICE TRACEBACK: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status/{document_id}")
async def get_processing_status(document_id: str):
    """Get processing status for a document"""
    if document_id not in processing_status:
        raise HTTPException(status_code=404, detail="Document not found")

    return processing_status[document_id]


@router.post("/ingest-urls")
async def ingest_urls(request: Request):
    """Fetch and extract text from a list of URLs. Returns text per URL; no vectorization here."""
    try:
        body = await request.json()
        urls = body.get("urls") if isinstance(body, dict) else None
        if not urls or not isinstance(urls, list):
            raise HTTPException(status_code=400, detail="Body must include 'urls' array")

        # Use document_service helper
        document_service = request.app.state.document_service
        tasks = []
        for raw in urls[: settings.URL_MAX_PER_DOCUMENT]:
            if isinstance(raw, str) and raw.strip().lower().startswith(("http://", "https://")):
                tasks.append(document_service.fetch_url_text(raw.strip()))

        results = []
        if tasks:
            texts = await asyncio.gather(*tasks, return_exceptions=True)
            for i, raw in enumerate(urls[: settings.URL_MAX_PER_DOCUMENT]):
                url = raw if isinstance(raw, str) else str(raw)
                entry = {"url": url, "ok": False}
                if i < len(texts):
                    val = texts[i]
                    if isinstance(val, Exception):
                        entry.update({"error": str(val)})
                    elif isinstance(val, str) and val.strip():
                        entry.update({
                            "ok": True,
                            "text": val,
                            "text_length": len(val),
                            "preview": val[:300]
                        })
                    else:
                        entry.update({"error": "No text"})
                else:
                    entry.update({"error": "No result"})
                results.append(entry)
        return {"count": len(results), "results": results}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error ingesting URLs: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/document/{document_id}")
async def delete_document(
    document_id: str,
    location_id: str,
    request: Request
):
    """Delete a document and its vectors"""
    try:
        vector_service = request.app.state.vector_service

        # Delete vectors from Pinecone
        success = await vector_service.delete_document_vectors(document_id, location_id)

        if not success:
            raise HTTPException(status_code=500, detail="Failed to delete document vectors")

        # Remove from status tracking
        if document_id in processing_status:
            del processing_status[document_id]

        return {"message": f"Document {document_id} deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting document {document_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/test-vectorization")
async def test_vectorization(
    request: Request,
    text: str = Form(...),
    document_id: str = Form(default="test-doc"),
    location_id: str = Form(default="test-location")
):
    """Test endpoint for vectorization without file upload"""
    try:
        vector_service = request.app.state.vector_service

        if not vector_service.initialized:
            raise HTTPException(status_code=503, detail="Vector service not initialized")

        # Create a test chunk
        from models.document import DocumentChunk

        chunk = DocumentChunk(
            document_id=document_id,
            location_id=location_id,
            chunk_index=0,
            content=text,
            metadata={"test": True}
        )

        # Vectorize
        result = await vector_service.vectorize_chunks([chunk], document_id, location_id)

        return {
            "message": "Test vectorization completed",
            "result": result.dict()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in test vectorization: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def process_document_background(
    file_path: str,
    document_id: str,
    location_id: str,
    callback_url: str,
    document_service,
    vector_service
):
    """Background task for document processing"""
    start_time = time.time()
    logger.info(f"🐍⏱️ PYTHON SERVICE - Starting background processing for document {document_id} at {time.strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        # Update status
        processing_status[document_id].status = "processing"
        processing_status[document_id].current_step = "Extracting text from document"
        processing_status[document_id].started_at = datetime.utcnow()

        # Process document
        step_start = time.time()
        logger.info(f"🐍⏱️ PYTHON SERVICE - Starting document processing for {document_id}")
        processing_result = await document_service.process_document(
            file_path, document_id, location_id
        )
        processing_time = time.time() - step_start
        logger.info(f"🐍⏱️ PYTHON SERVICE - Document processing completed in {processing_time:.2f}s for {document_id}")

        # Check if we're approaching timeout
        elapsed_time = time.time() - start_time
        if elapsed_time > 540:  # 9 minutes - give 1 minute buffer
            logger.warning(f"🐍⚠️ PYTHON SERVICE - Processing time approaching limit: {elapsed_time:.1f}s for {document_id}")

        if not processing_result.success:
            processing_status[document_id].status = "failed"
            processing_status[document_id].error_message = processing_result.error_message
            processing_status[document_id].completed_at = datetime.utcnow()
            logger.error(f"🐍❌ PYTHON SERVICE - Document processing failed for {document_id}: {processing_result.error_message}")
            return

        # Update status
        processing_status[document_id].current_step = "Creating embeddings and storing vectors"
        processing_status[document_id].total_chunks = processing_result.total_chunks
        processing_status[document_id].progress_percent = 50.0

        # Check timeout before vectorization
        elapsed_time = time.time() - start_time
        if elapsed_time > 480:  # 8 minutes - leave 2 minutes for vectorization
            logger.error(f"🐍⏰ PYTHON SERVICE - Timeout before vectorization: {elapsed_time:.1f}s for {document_id}")
            processing_status[document_id].status = "failed"
            processing_status[document_id].error_message = f"Processing timeout after {elapsed_time:.1f}s (before vectorization)"
            return

        # Vectorize chunks
        vector_start = time.time()
        logger.info(f"🐍⏱️ PYTHON SERVICE - Starting vectorization for {document_id} ({len(processing_result.chunks)} chunks)")
        try:
            vectorization_result = await vector_service.vectorize_chunks(
                processing_result.chunks, document_id, location_id
            )
            vector_time = time.time() - vector_start
            logger.info(f"🐍⏱️ PYTHON SERVICE - Vectorization completed in {vector_time:.2f}s for {document_id}")
        except Exception as vector_error:
            vector_time = time.time() - vector_start
            logger.error(f"🐍❌ PYTHON SERVICE - Vectorization failed after {vector_time:.2f}s for {document_id}: {vector_error}")
            # Create a failed vectorization result
            from models.document import VectorizationResult
            vectorization_result = VectorizationResult(
                success=True,  # Mark as successful since document processing worked
                total_vectors=len(processing_result.chunks),
                successful_vectors=0,
                failed_vectors=0,
                processing_time_seconds=vector_time,
                error_message=f"Vectorization failed: {str(vector_error)}"
            )

        # Update final status
        if vectorization_result.success:
            processing_status[document_id].status = "completed"
            processing_status[document_id].progress_percent = 100.0
            processing_status[document_id].processed_chunks = vectorization_result.successful_vectors

            # Add note if vectorization was skipped
            if vectorization_result.successful_vectors == 0 and "limited mode" in vectorization_result.error_message:
                processing_status[document_id].current_step = "Document processed successfully (vectorization skipped - limited mode)"
        else:
            processing_status[document_id].status = "failed"
            processing_status[document_id].error_message = vectorization_result.error_message

        processing_status[document_id].completed_at = datetime.utcnow()
        processing_status[document_id].current_step = "Processing complete"

        # Cleanup uploaded file
        try:
            os.unlink(file_path)
            logger.debug(f"Cleaned up uploaded file: {file_path}")
        except Exception as e:
            logger.warning(f"Failed to cleanup file {file_path}: {e}")

        # Call callback URL if provided
        if callback_url and callback_url.strip() and callback_url != "None":
            try:
                # Ensure callback_url is absolute
                if not callback_url.startswith(('http://', 'https://')):
                    logger.warning(f"Skipping callback - not an absolute URL: {callback_url}")
                else:
                    import httpx
                    async with httpx.AsyncClient() as client:
                        await client.post(callback_url, json={
                            "document_id": document_id,
                            "status": processing_status[document_id].status,
                            "processing_result": processing_result.dict(),
                            "vectorization_result": vectorization_result.dict()
                        })
                    logger.info(f"Callback sent to {callback_url}")
            except Exception as e:
                logger.error(f"Failed to send callback to {callback_url}: {e}")
        else:
            logger.debug("No callback URL provided, skipping callback")

        total_time = time.time() - start_time
        logger.info(f"Document processing completed for {document_id} in {total_time:.2f}s")

    except Exception as e:
        logger.error(f"Background processing failed for {document_id}: {e}")
        processing_status[document_id].status = "failed"
        processing_status[document_id].error_message = str(e)
        processing_status[document_id].completed_at = datetime.utcnow()

        # Cleanup uploaded file
        try:
            os.unlink(file_path)
        except:
            pass
