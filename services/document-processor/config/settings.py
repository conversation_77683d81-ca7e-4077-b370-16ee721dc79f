"""
Configuration settings for the Document Processing Service
"""

import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings"""

    # Service Configuration
    SERVICE_NAME: str = Field(default="document-processor", env="SERVICE_NAME")
    SERVICE_VERSION: str = Field(default="1.0.0", env="SERVICE_VERSION")
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")

    # OpenAI Configuration
    OPENAI_API_KEY: str = Field(default="", env="OPENAI_API_KEY")
    OPENAI_MODEL: str = Field(default="text-embedding-3-small", env="OPENAI_MODEL")

    # MongoDB Vector Store Configuration
    MONGODB_URI: str = Field(default="", env="MONGODB_URI")
    MONGODB_DB_NAME: str = Field(default="bakedbot", env="MONGODB_DB_NAME")
    MONGODB_VECTOR_INDEX_NAME: str = Field(default="vector_index", env="MONGODB_VECTOR_INDEX_NAME")
    MONGODB_COLLECTION_PREFIX: str = Field(default="", env="MONGODB_COLLECTION_PREFIX")

    # Redis Configuration
    REDIS_URL: str = Field(default="redis://redis:6379/0", env="REDIS_URL")

    # File Processing Configuration
    MAX_FILE_SIZE_MB: int = Field(default=10, env="MAX_FILE_SIZE_MB")
    MAX_CHUNKS_PER_DOCUMENT: int = Field(default=100, env="MAX_CHUNKS_PER_DOCUMENT")
    CHUNK_SIZE: int = Field(default=1000, env="CHUNK_SIZE")
    CHUNK_OVERLAP: int = Field(default=200, env="CHUNK_OVERLAP")

    # Upload Configuration
    UPLOAD_DIR: str = Field(default="/app/uploads", env="UPLOAD_DIR")
    ALLOWED_EXTENSIONS: list = Field(default=[".pdf", ".txt", ".md"], env="ALLOWED_EXTENSIONS")

    # Processing Configuration
    BATCH_SIZE: int = Field(default=10, env="BATCH_SIZE")  # Vectors per batch
    PROCESSING_DELAY_MS: int = Field(default=100, env="PROCESSING_DELAY_MS")


    # Web link ingestion configuration
    ENABLE_WEB_LINK_INGEST: bool = Field(default=True, env="ENABLE_WEB_LINK_INGEST")
    URL_FETCH_CONCURRENCY: int = Field(default=4, env="URL_FETCH_CONCURRENCY")
    URL_FETCH_TIMEOUT_SECONDS: int = Field(default=10, env="URL_FETCH_TIMEOUT_SECONDS")
    URL_MAX_PER_DOCUMENT: int = Field(default=10, env="URL_MAX_PER_DOCUMENT")
    URL_MAX_CONTENT_LENGTH: int = Field(default=200000, env="URL_MAX_CONTENT_LENGTH")  # characters

    # Robots.txt and web chunk caps
    RESPECT_ROBOTS_TXT: bool = Field(default=True, env="RESPECT_ROBOTS_TXT")
    CRAWLER_USER_AGENT: str = Field(default="BakedBot-DocProcessor/1.0", env="CRAWLER_USER_AGENT")
    MAX_WEB_CHUNKS_PER_DOCUMENT: int = Field(default=200, env="MAX_WEB_CHUNKS_PER_DOCUMENT")

    # Domain allow/deny lists (comma-separated)
    ALLOWED_DOMAINS: str = Field(default="", env="ALLOWED_DOMAINS")
    BLOCKED_DOMAINS: str = Field(default="", env="BLOCKED_DOMAINS")



    class Config:
        env_file = ".env"
        case_sensitive = True


# Create global settings instance
settings = Settings()
