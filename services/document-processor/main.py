"""
Document Processing Microservice
FastAPI-based service for PDF parsing, text chunking, and vectorization
"""

import os
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger
import psutil

from config.settings import settings
from services.document_service import DocumentService
from services.vector_service import VectorService
from api.routes import router
from utils.logging_config import setup_logging


# Global services
document_service = None
vector_service = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global document_service, vector_service
    
    # Startup
    logger.info("🚀 Starting Document Processing Service...")
    logger.info(f"Service: {settings.SERVICE_NAME} v{settings.SERVICE_VERSION}")
    logger.info(f"Max file size: {settings.MAX_FILE_SIZE_MB}MB")
    logger.info(f"Max chunks per document: {settings.MAX_CHUNKS_PER_DOCUMENT}")
    
    # Initialize services
    try:
        document_service = DocumentService()
        vector_service = VectorService()
        await vector_service.initialize()
        
        # Store services in app state
        app.state.document_service = document_service
        app.state.vector_service = vector_service
        
        logger.info("✅ Document Processing Service started successfully")
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize services: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("🛑 Shutting down Document Processing Service...")
    if vector_service:
        await vector_service.cleanup()
    logger.info("✅ Shutdown complete")


# Create FastAPI app
app = FastAPI(
    title="Document Processing Service",
    description="Microservice for PDF parsing, text chunking, and vectorization",
    version=settings.SERVICE_VERSION,
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(router, prefix="/api/v1")


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    memory_info = psutil.virtual_memory()
    process = psutil.Process()
    
    return {
        "status": "healthy",
        "service": settings.SERVICE_NAME,
        "version": settings.SERVICE_VERSION,
        "memory": {
            "total_gb": round(memory_info.total / (1024**3), 2),
            "available_gb": round(memory_info.available / (1024**3), 2),
            "used_percent": memory_info.percent,
            "process_memory_mb": round(process.memory_info().rss / (1024**2), 2)
        },
        "settings": {
            "max_file_size_mb": settings.MAX_FILE_SIZE_MB,
            "max_chunks": settings.MAX_CHUNKS_PER_DOCUMENT,
            "chunk_size": settings.CHUNK_SIZE,
            "chunk_overlap": settings.CHUNK_OVERLAP
        }
    }


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": settings.SERVICE_NAME,
        "version": settings.SERVICE_VERSION,
        "status": "running",
        "docs": "/docs",
        "health": "/health"
    }


if __name__ == "__main__":
    import uvicorn
    
    # Setup logging
    setup_logging()
    
    # Run the application
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level=settings.LOG_LEVEL.lower()
    )
