"""
Data models for document processing
"""

from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from datetime import datetime


class DocumentChunk(BaseModel):
    """Represents a chunk of text from a document"""
    document_id: str
    location_id: str
    chunk_index: int
    content: str
    metadata: Dict[str, Any] = Field(default_factory=dict)
    vector_id: Optional[str] = None  # Set after vectorization
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ProcessingResult(BaseModel):
    """Result of document processing"""
    document_id: str
    location_id: str
    file_path: str
    file_size_bytes: int
    mime_type: str
    total_chunks: int
    chunks: List[DocumentChunk]
    processing_time_seconds: float
    success: bool
    error_message: Optional[str] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class VectorizationResult(BaseModel):
    """Result of vectorization process"""
    document_id: str
    location_id: str
    total_vectors: int
    successful_vectors: int
    failed_vectors: int
    processing_time_seconds: float
    success: bool
    error_message: Optional[str] = None
    vector_ids: List[str] = Field(default_factory=list)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ProcessingRequest(BaseModel):
    """Request for document processing"""
    document_id: str
    location_id: str
    file_path: str
    callback_url: Optional[str] = None  # URL to notify when complete
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ProcessingStatus(BaseModel):
    """Status of document processing"""
    document_id: str
    location_id: str
    status: str  # pending, processing, completed, failed
    progress_percent: float = 0.0
    current_step: str = ""
    total_chunks: int = 0
    processed_chunks: int = 0
    error_message: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
