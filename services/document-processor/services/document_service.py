"""
Document processing service for PDF parsing and text extraction
"""

import os
import gc
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import re
import html as html_lib
import magic
import PyPDF2
import fitz  # pymupdf
import pdfplumber
import httpx
from loguru import logger
try:
    import trafilatura
except Exception:
    trafilatura = None  # optional dependency

from config.settings import settings
from models.document import DocumentChunk, ProcessingResult


class DocumentService:
    """Service for processing documents and extracting text"""

    def __init__(self):
        self.upload_dir = Path(settings.UPLOAD_DIR)
        self.upload_dir.mkdir(exist_ok=True)
        logger.info(f"Document service initialized with upload dir: {self.upload_dir}")

    async def process_document(
        self,
        file_path: str,
        document_id: str,
        location_id: str
    ) -> ProcessingResult:
        """
        Process a document and extract text chunks

        Args:
            file_path: Path to the document file
            document_id: Unique document identifier
            location_id: Location identifier for namespacing

        Returns:
            ProcessingResult with extracted chunks and metadata
        """
        try:
            logger.info(f"Processing document: {file_path} (ID: {document_id})")

            # Validate file
            file_info = await self._validate_file(file_path)
            if not file_info["valid"]:
                raise ValueError(f"Invalid file: {file_info['error']}")

            # Extract text based on file type
            text_content = await self._extract_text(file_path, file_info["mime_type"])

            if not text_content.strip():
                raise ValueError("No text content extracted from document")

            # Create text chunks
            chunks = await self._create_chunks(text_content, document_id, location_id)

            # Optionally detect and ingest web links within the extracted text
            if settings.ENABLE_WEB_LINK_INGEST:
                try:
                    web_chunks = await self._extract_and_fetch_urls(
                        text_content,
                        document_id,
                        location_id,
                    )
                    if web_chunks:
                        # Tag original document chunks for consistency
                        for ch in chunks:
                            ch.metadata.setdefault("source_type", "document")
                        # Append web-derived chunks
                        chunks.extend(web_chunks)
                        # Reindex chunk indices for uniqueness/ordering
                        for idx, ch in enumerate(chunks):
                            ch.chunk_index = idx
                except Exception as url_err:
                    logger.warning(f"URL ingestion encountered an error but will not fail processing: {url_err}")

            # Create processing result
            result = ProcessingResult(
                document_id=document_id,
                location_id=location_id,
                file_path=file_path,
                file_size_bytes=file_info["size"],
                mime_type=file_info["mime_type"],
                total_chunks=len(chunks),
                chunks=chunks,
                processing_time_seconds=0,  # Will be calculated by caller
                success=True,
                error_message=None
            )

            logger.info(f"Document processed successfully: {len(chunks)} chunks extracted (including web links if any)")
            return result

        except Exception as e:
            logger.error(f"Error processing document {file_path}: {e}")
            return ProcessingResult(
                document_id=document_id,
                location_id=location_id,
                file_path=file_path,
                file_size_bytes=0,
                mime_type="",
                total_chunks=0,
                chunks=[],
                processing_time_seconds=0,
                success=False,
                error_message=str(e)
            )
        finally:

            # Force garbage collection
            gc.collect()

    async def _validate_file(self, file_path: str) -> Dict[str, Any]:
        """Validate file size, type, and accessibility"""
        try:
            path = Path(file_path)

            if not path.exists():
                return {"valid": False, "error": "File does not exist"}

            # Check file size
            file_size = path.stat().st_size
            max_size_bytes = settings.MAX_FILE_SIZE_MB * 1024 * 1024

            if file_size > max_size_bytes:
                return {
                    "valid": False,
                    "error": f"File too large: {file_size / (1024*1024):.1f}MB > {settings.MAX_FILE_SIZE_MB}MB"
                }

            # Check file type
            mime_type = magic.from_file(file_path, mime=True)
            file_extension = path.suffix.lower()

            if file_extension not in settings.ALLOWED_EXTENSIONS:
                return {
                    "valid": False,
                    "error": f"Unsupported file type: {file_extension}"
                }

            return {
                "valid": True,
                "size": file_size,
                "mime_type": mime_type,
                "extension": file_extension
            }

        except Exception as e:
            return {"valid": False, "error": f"File validation error: {e}"}

    async def _extract_text(self, file_path: str, mime_type: str) -> str:
        """Extract text from document based on file type"""
        path = Path(file_path)
        extension = path.suffix.lower()

        try:
            if extension == ".pdf":
                return await self._extract_pdf_text(file_path)
            elif extension in [".txt", ".md"]:
                return await self._extract_text_file(file_path)
            else:
                raise ValueError(f"Unsupported file type: {extension}")

        except Exception as e:
            logger.error(f"Text extraction failed for {file_path}: {e}")
            raise

    async def _extract_pdf_text(self, file_path: str) -> str:
        """Extract text from PDF using multiple methods for reliability"""
        text_content = ""

        # Method 1: Try pdfplumber (best for complex layouts)
        try:
            logger.debug("Trying pdfplumber for PDF extraction")
            with pdfplumber.open(file_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    if page_num >= 50:  # Limit pages for memory safety
                        logger.warning(f"Limiting PDF to first 50 pages for memory safety")
                        break

                    page_text = page.extract_text()
                    if page_text:
                        text_content += page_text + "\n\n"

                    # Memory cleanup
                    if page_num % 10 == 0:
                        gc.collect()
                        await asyncio.sleep(0.01)  # Allow other tasks

            if text_content.strip():
                logger.info(f"PDF text extracted successfully using pdfplumber")
                return text_content

        except Exception as e:
            logger.warning(f"pdfplumber failed: {e}, trying PyMuPDF")

        # Method 2: Try PyMuPDF (fitz)
        try:
            logger.debug("Trying PyMuPDF for PDF extraction")
            doc = fitz.open(file_path)

            for page_num in range(min(doc.page_count, 50)):  # Limit pages
                page = doc[page_num]
                page_text = page.get_text()
                if page_text:
                    text_content += page_text + "\n\n"

                # Memory cleanup
                if page_num % 10 == 0:
                    gc.collect()
                    await asyncio.sleep(0.01)

            doc.close()

            if text_content.strip():
                logger.info(f"PDF text extracted successfully using PyMuPDF")
                return text_content

        except Exception as e:
            logger.warning(f"PyMuPDF failed: {e}, trying PyPDF2")

        # Method 3: Try PyPDF2 (fallback)
        try:
            logger.debug("Trying PyPDF2 for PDF extraction")
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)

                for page_num in range(min(len(pdf_reader.pages), 50)):  # Limit pages
                    page = pdf_reader.pages[page_num]
                    page_text = page.extract_text()
                    if page_text:
                        text_content += page_text + "\n\n"

                    # Memory cleanup
                    if page_num % 10 == 0:
                        gc.collect()
                        await asyncio.sleep(0.01)

            if text_content.strip():
                logger.info(f"PDF text extracted successfully using PyPDF2")
                return text_content

        except Exception as e:
            logger.error(f"PyPDF2 failed: {e}")

        # If all methods failed
        if not text_content.strip():
            raise ValueError("Could not extract text from PDF using any method")

        return text_content

    async def _extract_text_file(self, file_path: str) -> str:
        """Extract text from plain text files"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read()
        except UnicodeDecodeError:
            # Try with different encoding
            with open(file_path, 'r', encoding='latin-1') as file:
                return file.read()

    async def _create_chunks(
        self,
        text: str,
        document_id: str,
        location_id: str
    ) -> List[DocumentChunk]:
        """Create text chunks from extracted text"""
        chunks: List[DocumentChunk] = []

        # Simple chunking strategy - split by sentences and group
        sentences = self._split_into_sentences(text)

        current_chunk = ""
        chunk_index = 0

        for sentence in sentences:
            # Check if adding this sentence would exceed chunk size
            if len(current_chunk) + len(sentence) > settings.CHUNK_SIZE:
                if current_chunk.strip():
                    # Create chunk
                    chunk = DocumentChunk(
                        document_id=document_id,
                        location_id=location_id,
                        chunk_index=chunk_index,
                        content=current_chunk.strip(),
                        metadata={
                            "chunk_size": len(current_chunk),
                            "sentence_count": current_chunk.count('.') + current_chunk.count('!') + current_chunk.count('?')
                        }
                    )
                    chunks.append(chunk)
                    chunk_index += 1

                    # Check chunk limit
                    if len(chunks) >= settings.MAX_CHUNKS_PER_DOCUMENT:
                        logger.warning(f"Reached maximum chunks limit: {settings.MAX_CHUNKS_PER_DOCUMENT}")
                        break

                    # Start new chunk with overlap
                    overlap_text = self._get_overlap_text(current_chunk, settings.CHUNK_OVERLAP)
                    current_chunk = overlap_text + sentence
                else:
                    current_chunk = sentence
            else:
                current_chunk += sentence

        # Add final chunk if there's remaining content
        if current_chunk.strip() and len(chunks) < settings.MAX_CHUNKS_PER_DOCUMENT:
            chunk = DocumentChunk(
                document_id=document_id,
                location_id=location_id,
                chunk_index=chunk_index,
                content=current_chunk.strip(),
                metadata={
                    "chunk_size": len(current_chunk),
                    "sentence_count": current_chunk.count('.') + current_chunk.count('!') + current_chunk.count('?')
                }
            )
            chunks.append(chunk)

        logger.info(f"Created {len(chunks)} chunks from document")
        return chunks

    async def _extract_and_fetch_urls(self, text: str, document_id: str, location_id: str) -> List[DocumentChunk]:
        """Detect URLs in text, fetch and extract readable text, and return as chunks.
        Failures on individual URLs are tolerated.
        """
        # Regex to detect http/https URLs, avoiding trailing punctuation
        url_regex = re.compile(r"\bhttps?://[^\s)>'\"]+", re.IGNORECASE)
        urls = list(dict.fromkeys(url_regex.findall(text)))  # dedupe while preserving order
        if not urls:
            return []

        # Limit number of URLs per document
        urls = urls[: settings.URL_MAX_PER_DOCUMENT]

        timeout = httpx.Timeout(settings.URL_FETCH_TIMEOUT_SECONDS)
        limits = httpx.Limits(max_keepalive_connections=5, max_connections=5)
        chunks: List[DocumentChunk] = []

        async def fetch_and_extract(url: str) -> Optional[str]:
            try:
                headers = {"User-Agent": settings.CRAWLER_USER_AGENT}
                async with httpx.AsyncClient(timeout=timeout, follow_redirects=True, limits=limits, headers=headers) as client:
                    from urllib.parse import urlparse
                    parsed = urlparse(url)
                    hostname = (parsed.netloc or "").lower()

                    # Domain allow/deny checks
                    allow_str = (settings.ALLOWED_DOMAINS or "").strip()
                    deny_str = (settings.BLOCKED_DOMAINS or "").strip()
                    allowed = {d.strip().lower() for d in allow_str.split(",") if d.strip()}
                    denied = {d.strip().lower() for d in deny_str.split(",") if d.strip()}
                    if denied and any(hostname.endswith(d) for d in denied):
                        logger.info(f"Skipping URL due to BLOCKED_DOMAINS: {url}")
                        return None
                    if allowed and not any(hostname.endswith(d) for d in allowed):
                        logger.info(f"Skipping URL not in ALLOWED_DOMAINS: {url}")
                        return None

                    # Optional robots.txt check
                    if settings.RESPECT_ROBOTS_TXT:
                        try:
                            robots_url = f"{parsed.scheme}://{parsed.netloc}/robots.txt"
                            robots_allow = True
                            try:
                                r = await client.get(robots_url)
                                if r.status_code == 200 and r.text:
                                    robots_allow = self._is_allowed_by_robots(r.text, url, settings.CRAWLER_USER_AGENT)
                            except Exception:
                                # If robots can’t be fetched, default to allow
                                robots_allow = True
                            if not robots_allow:
                                logger.info(f"Skipping URL due to robots.txt: {url}")
                                return None
                        except Exception as re_err:
                            logger.debug(f"robots.txt check skipped due to error: {re_err}")

                    resp = await client.get(url)
                    if resp.status_code != 200:
                        logger.warning(f"Skipping URL {url}: status {resp.status_code}")
                        return None
                    ctype = resp.headers.get("content-type", "")
                    if "text/html" not in ctype:
                        logger.warning(f"Skipping URL {url}: non-HTML content-type {ctype}")
                        return None
                    html = resp.text or ""
                    text_only = self._html_to_readable_text(html)
                    if not text_only or not text_only.strip():
                        return None
                    # Cap content length
                    if len(text_only) > settings.URL_MAX_CONTENT_LENGTH:
                        text_only = text_only[: settings.URL_MAX_CONTENT_LENGTH]
                    return text_only
            except Exception as e:
                logger.warning(f"Failed to fetch/extract URL {url}: {e}")
                return None

        # Concurrency control
        sem = asyncio.Semaphore(settings.URL_FETCH_CONCURRENCY)

        async def worker(url: str) -> Optional[Tuple[str, str]]:
            async with sem:
                content = await fetch_and_extract(url)
                if content:
                    return (url, content)
                return None

        results = await asyncio.gather(*(worker(u) for u in urls), return_exceptions=True)

        # Build chunks for each successful URL
        successes = []
        for res in results:
            if isinstance(res, Exception) or res is None:
                continue
            successes.append(res)

        total_added = 0
        for url, content in successes:
            if settings.MAX_WEB_CHUNKS_PER_DOCUMENT and total_added >= settings.MAX_WEB_CHUNKS_PER_DOCUMENT:
                logger.info(f"Reached MAX_WEB_CHUNKS_PER_DOCUMENT={settings.MAX_WEB_CHUNKS_PER_DOCUMENT}, skipping remaining web content")
                break
            # Split into chunks using existing chunker to keep consistency
            url_chunks = await self._create_chunks(content, document_id, location_id)
            # Apply cap if needed
            remaining = settings.MAX_WEB_CHUNKS_PER_DOCUMENT - total_added
            if settings.MAX_WEB_CHUNKS_PER_DOCUMENT and remaining < len(url_chunks):
                url_chunks = url_chunks[:max(0, remaining)]
            # Annotate metadata for web source
            for ch in url_chunks:
                ch.metadata.update({
                    "source_type": "web",
                    "source_url": url,
                })
            chunks.extend(url_chunks)
            total_added += len(url_chunks)

        logger.info(f"Created {total_added} web-derived chunks from {len(successes)} URLs")
        return chunks

    def _html_to_readable_text(self, html: str) -> str:
        """Prefer high-quality readable extraction via trafilatura, with fallback to simple stripping."""
        # Try trafilatura if available
        if trafilatura is not None:
            try:
                extracted = trafilatura.extract(html, include_tables=False, include_formatting=False)
                if extracted and extracted.strip():
                    # Normalize whitespace
                    extracted = re.sub(r"\s+", " ", extracted).strip()
                    return extracted
            except Exception as e:
                logger.warning(f"trafilatura extract failed, falling back to simple extraction: {e}")
        # Fallback: simple HTML stripping
        html = re.sub(r"<script[^>]*>.*?</script>", " ", html, flags=re.IGNORECASE | re.DOTALL)
        html = re.sub(r"<style[^>]*>.*?</style>", " ", html, flags=re.IGNORECASE | re.DOTALL)
        html = re.sub(r"<head[^>]*>.*?</head>", " ", html, flags=re.IGNORECASE | re.DOTALL)
        html = re.sub(r"</?(p|div|br|li|ul|ol|h[1-6]|section|article|table|tr|td|th)[^>]*>", "\n", html, flags=re.IGNORECASE)
        html = re.sub(r"<[^>]+>", " ", html)
        text = html_lib.unescape(html)
        text = re.sub(r"\s+", " ", text)
        return text.strip()

    # Simple robots.txt parsing: allow unless a matching Disallow exists for the path
    # Note: minimal, not a full robots implementation
    def _is_allowed_by_robots(robots_txt: str, url: str, user_agent: str) -> bool:
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            path = parsed.path or "/"
            ua = user_agent.split("/")[0].lower()

            # Parse records for relevant user-agents: specific UA or *
            lines = [l.strip() for l in robots_txt.splitlines()]
            groups = []
            current: Dict[str, Any] = {"agents": [], "rules": []}
            for line in lines:
                if not line or line.startswith("#"):
                    continue
                if line.lower().startswith("user-agent:"):
                    # Start new group when UA encountered after rules
                    if current["agents"] or current["rules"]:
                        groups.append(current)
                        current = {"agents": [], "rules": []}
                    current["agents"].append(line.split(":", 1)[1].strip().lower())
                elif line.lower().startswith("disallow:"):
                    rule = line.split(":", 1)[1].strip() or "/"
                    current["rules"].append(("disallow", rule))
                elif line.lower().startswith("allow:"):
                    rule = line.split(":", 1)[1].strip() or "/"
                    current["rules"].append(("allow", rule))
            if current["agents"] or current["rules"]:
                groups.append(current)

            # Gather applicable rules
            applicable: list[tuple[str, str]] = []
            for g in groups:
                if "*" in g["agents"] or any(ua in agent for agent in g["agents"]):
                    applicable.extend(g["rules"])

            # If there are no rules, allow
            if not applicable:
                return True

            # Apply longest-match precedence: allow beats disallow on longer match
            def longest_prefix_length(prefix: str, s: str) -> int:
                return len(prefix) if s.startswith(prefix) else -1

            best_allow = -1
            best_disallow = -1
            for kind, rule in applicable:
                plen = longest_prefix_length(rule, path)
                if plen >= 0:
                    if kind == "allow":
                        best_allow = max(best_allow, plen)
                    else:
                        best_disallow = max(best_disallow, plen)
            if best_allow >= best_disallow:
                return True
            return False
        except Exception:
            # Default to allow on parsing error
            return True


    def _split_into_sentences(self, text: str) -> List[str]:
        """Split text into sentences"""
        import re

        # Simple sentence splitting - can be improved with NLTK
        sentences = re.split(r'(?<=[.!?])\s+', text)
        return [s.strip() for s in sentences if s.strip()]

    def _get_overlap_text(self, text: str, overlap_size: int) -> str:
        """Get overlap text from the end of current chunk"""
        if len(text) <= overlap_size:
            return text
        return text[-overlap_size:]


    async def fetch_url_text(self, url: str) -> Optional[str]:
        """Fetch a single URL and extract readable text using existing helpers/settings."""
        try:
            timeout = httpx.Timeout(settings.URL_FETCH_TIMEOUT_SECONDS)
            limits = httpx.Limits(max_keepalive_connections=5, max_connections=5)
            headers = {"User-Agent": settings.CRAWLER_USER_AGENT}
            from urllib.parse import urlparse

            parsed = urlparse(url)
            hostname = (parsed.netloc or "").lower()

            # Domain allow/deny checks
            allow_str = (settings.ALLOWED_DOMAINS or "").strip()
            deny_str = (settings.BLOCKED_DOMAINS or "").strip()
            allowed = {d.strip().lower() for d in allow_str.split(",") if d.strip()}
            denied = {d.strip().lower() for d in deny_str.split(",") if d.strip()}
            if denied and any(hostname.endswith(d) for d in denied):
                logger.info(f"Skipping URL due to BLOCKED_DOMAINS: {url}")
                return None
            if allowed and not any(hostname.endswith(d) for d in allowed):
                logger.info(f"Skipping URL not in ALLOWED_DOMAINS: {url}")
                return None

            async with httpx.AsyncClient(timeout=timeout, follow_redirects=True, limits=limits, headers=headers) as client:
                # Optional robots.txt check
                if settings.RESPECT_ROBOTS_TXT:
                    try:
                        robots_url = f"{parsed.scheme}://{parsed.netloc}/robots.txt"
                        robots_allow = True
                        try:
                            r = await client.get(robots_url)
                            if r.status_code == 200 and r.text:
                                robots_allow = self._is_allowed_by_robots(r.text, url, settings.CRAWLER_USER_AGENT)
                        except Exception:
                            robots_allow = True
                        if not robots_allow:
                            logger.info(f"Skipping URL due to robots.txt: {url}")
                            return None
                    except Exception as re_err:
                        logger.debug(f"robots.txt check skipped due to error: {re_err}")

                resp = await client.get(url)
                if resp.status_code != 200:
                    logger.warning(f"Skipping URL {url}: status {resp.status_code}")
                    return None
                ctype = resp.headers.get("content-type", "")
                if "text/html" not in ctype:
                    logger.warning(f"Skipping URL {url}: non-HTML content-type {ctype}")
                    return None
                html = resp.text or ""
                text_only = self._html_to_readable_text(html)
                if not text_only or not text_only.strip():
                    return None
                if len(text_only) > settings.URL_MAX_CONTENT_LENGTH:
                    text_only = text_only[: settings.URL_MAX_CONTENT_LENGTH]
                return text_only
        except Exception as e:
            logger.warning(f"Failed to fetch URL {url}: {e}")
            return None
