"""
Vector service for generating embeddings and storing in MongoDB Atlas Vector Search
"""

import asyncio
import gc
import time
from typing import List, Dict, Any, Optional
import openai
from pymongo import MongoClient
from loguru import logger

from config.settings import settings
from models.document import DocumentChunk, VectorizationResult


class VectorService:
    """Service for generating embeddings and storing vectors"""
    
    def __init__(self):
        self.openai_client = None
        self.mongo_client: Optional[MongoClient] = None
        self.mongo_db = None
        self.collection_name = None
        self.initialized = False
    
    async def initialize(self):
        """Initialize OpenAI and MongoDB clients"""
        try:
            logger.info("Initializing Vector Service...")

            # Check if API keys are available
            if not settings.OPENAI_API_KEY:
                logger.warning("⚠️ OPENAI_API_KEY not set - vector service will be limited")
                self.initialized = False
                return

            if not settings.MONGODB_URI:
                logger.warning("⚠️ MONGODB_URI not set - vector service will be limited")
                self.initialized = False
                return

            # Initialize OpenAI client
            self.openai_client = openai.AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
            logger.info("✅ OpenAI client initialized")

            # Initialize MongoDB client
            self.mongo_client = MongoClient(settings.MONGODB_URI)
            self.mongo_db = self.mongo_client[settings.MONGODB_DB_NAME]
            # Use the same collection/index name as the Node platform expects (DOCUMENT_INDEX = "document-embeddings")
            self.collection_name = f"{settings.MONGODB_COLLECTION_PREFIX}document-embeddings"
            logger.info(f"✅ Connected to MongoDB database: {settings.MONGODB_DB_NAME}")

            self.initialized = True
            logger.info("✅ Vector Service initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Vector Service: {e}")
            logger.warning("⚠️ Vector service will run in limited mode - document processing will work but vectorization will be skipped")
            self.initialized = False
    
    async def vectorize_chunks(
        self, 
        chunks: List[DocumentChunk],
        document_id: str,
        location_id: str
    ) -> VectorizationResult:
        """
        Generate embeddings for chunks and store in Pinecone
        
        Args:
            chunks: List of document chunks to vectorize
            document_id: Document identifier
            location_id: Location identifier for namespacing
            
        Returns:
            VectorizationResult with processing statistics
        """
        if not self.initialized:
            logger.warning("⚠️ Vector service not initialized - skipping vectorization")
            return VectorizationResult(
                success=True,  # Mark as successful since document processing worked
                total_vectors=len(chunks),
                successful_vectors=0,  # No vectors were created
                failed_vectors=0,  # Not failed, just skipped
                processing_time_seconds=0.0,
                error_message="Vectorization skipped - vector service running in limited mode (no API keys)"
            )
        
        start_time = time.time()
        successful_vectors = 0
        failed_vectors = 0
        vector_ids = []
        
        try:
            logger.info(f"Starting vectorization of {len(chunks)} chunks for document {document_id}")
            
            # Process chunks in batches
            batch_size = settings.BATCH_SIZE
            
            for i in range(0, len(chunks), batch_size):
                batch = chunks[i:i + batch_size]
                logger.debug(f"Processing batch {i//batch_size + 1}/{(len(chunks) + batch_size - 1)//batch_size}")
                
                try:
                    # Generate embeddings for batch
                    embeddings = await self._generate_embeddings([chunk.content for chunk in batch])
                    
                    # Prepare docs for MongoDB
                    vectors = []
                    for j, (chunk, embedding) in enumerate(zip(batch, embeddings)):
                        vector_id = f"{location_id}_{document_id}_{chunk.chunk_index}"
                        
                        vectors.append({
                            "id": vector_id,
                            "values": embedding,
                            "metadata": {
                                "document_id": document_id,
                                "location_id": location_id,
                                "chunk_index": chunk.chunk_index,
                                "content": chunk.content[:1000],  # Limit metadata size
                                "chunk_size": len(chunk.content),
                                **chunk.metadata
                            }
                        })
                        
                        # Update chunk with vector ID
                        chunk.vector_id = vector_id
                        vector_ids.append(vector_id)
                    
                    # Store docs in MongoDB
                    namespace = f"location_{location_id}"
                    from pymongo import UpdateOne
                    ops = []
                    for v in vectors:
                        ops.append(UpdateOne(
                            {"_id": v["id"]},
                            {"$set": {
                                "embedding": v["values"],
                                "metadata": v["metadata"],
                                "namespace": namespace,
                                "updated_at": int(time.time() * 1000),
                            }, "$setOnInsert": {"created_at": int(time.time() * 1000)}} ,
                            upsert=True
                        ))
                    if ops:
                        self.mongo_db[self.collection_name].bulk_write(ops, ordered=False)

                    successful_vectors += len(vectors)
                    logger.debug(f"Successfully stored {len(vectors)} vectors in batch")
                    
                except Exception as e:
                    logger.error(f"Failed to process batch {i//batch_size + 1}: {e}")
                    failed_vectors += len(batch)
                
                # Memory cleanup and delay
                gc.collect()
                if settings.PROCESSING_DELAY_MS > 0:
                    await asyncio.sleep(settings.PROCESSING_DELAY_MS / 1000)
            
            processing_time = time.time() - start_time
            success = failed_vectors == 0
            
            result = VectorizationResult(
                document_id=document_id,
                location_id=location_id,
                total_vectors=len(chunks),
                successful_vectors=successful_vectors,
                failed_vectors=failed_vectors,
                processing_time_seconds=processing_time,
                success=success,
                error_message=None if success else f"{failed_vectors} vectors failed to process",
                vector_ids=vector_ids
            )
            
            logger.info(f"Vectorization completed: {successful_vectors}/{len(chunks)} successful in {processing_time:.2f}s")
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Vectorization failed for document {document_id}: {e}")
            
            return VectorizationResult(
                document_id=document_id,
                location_id=location_id,
                total_vectors=len(chunks),
                successful_vectors=successful_vectors,
                failed_vectors=len(chunks) - successful_vectors,
                processing_time_seconds=processing_time,
                success=False,
                error_message=str(e),
                vector_ids=vector_ids
            )
    
    async def _generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings using OpenAI API"""
        try:
            logger.debug(f"Generating embeddings for {len(texts)} texts")
            
            # Clean and prepare texts
            cleaned_texts = []
            for text in texts:
                # Remove excessive whitespace and limit length
                cleaned_text = " ".join(text.split())
                if len(cleaned_text) > 8000:  # OpenAI limit is ~8191 tokens
                    cleaned_text = cleaned_text[:8000]
                cleaned_texts.append(cleaned_text)
            
            # Generate embeddings
            response = await self.openai_client.embeddings.create(
                model=settings.OPENAI_MODEL,
                input=cleaned_texts,
                encoding_format="float"
            )
            
            embeddings = [data.embedding for data in response.data]
            logger.debug(f"Generated {len(embeddings)} embeddings")
            
            return embeddings
            
        except Exception as e:
            logger.error(f"Failed to generate embeddings: {e}")
            raise
    
    async def delete_document_vectors(self, document_id: str, location_id: str) -> bool:
        """Delete all vectors for a document"""
        try:
            if not self.initialized:
                logger.warning("⚠️ Vector service not initialized - skipping vector deletion")
                return False

            namespace = f"location_{location_id}"

            # Delete all vectors for document in MongoDB
            result = self.mongo_db[self.collection_name].delete_many({
                "namespace": namespace,
                "metadata.document_id": document_id
            })
            logger.info(f"Deleted {result.deleted_count} vectors for document {document_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete vectors for document {document_id}: {e}")
            return False
    
    async def cleanup(self):
        """Cleanup resources"""
        logger.info("Cleaning up Vector Service...")
        self.initialized = False
        # Pinecone client doesn't need explicit cleanup
        logger.info("✅ Vector Service cleanup complete")
