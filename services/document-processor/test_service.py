#!/usr/bin/env python3
"""
Test script for the document processing service
"""

import asyncio
import sys
from pathlib import Path

# Add the service directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from services.document_service import DocumentService
from services.vector_service import VectorService
from config.settings import settings
from utils.logging_config import setup_logging


async def test_document_service():
    """Test the document service functionality"""
    print("🧪 Testing Document Service...")
    
    # Setup logging
    setup_logging()
    
    # Initialize services
    document_service = DocumentService()
    vector_service = VectorService()
    
    try:
        # Test vector service initialization
        print("📡 Initializing Vector Service...")
        await vector_service.initialize()
        print("✅ Vector Service initialized successfully")
        
        # Test document processing with a simple text file
        print("📄 Testing document processing...")
        
        # Create a test text file
        test_file_path = Path("test_document.txt")
        test_content = """
        This is a test document for the BakedBot AI platform.
        
        The document processing service should be able to:
        1. Extract text from various file formats
        2. Split text into meaningful chunks
        3. Generate embeddings for each chunk
        4. Store vectors in Pinecone
        
        This test verifies that the Python microservice can handle
        document processing without the memory issues that plagued
        the Node.js implementation.
        """
        
        with open(test_file_path, 'w') as f:
            f.write(test_content)
        
        # Process the document
        result = await document_service.process_document(
            str(test_file_path),
            "test-doc-123",
            "test-location-456"
        )
        
        print(f"📊 Processing Result:")
        print(f"   Success: {result.success}")
        print(f"   Total chunks: {result.total_chunks}")
        print(f"   File size: {result.file_size_bytes} bytes")
        print(f"   Processing time: {result.processing_time_seconds:.2f}s")
        
        if result.success and result.chunks:
            print("🔤 Sample chunk content:")
            print(f"   Chunk 0: {result.chunks[0].content[:100]}...")
            
            # Test vectorization
            print("🧠 Testing vectorization...")
            vector_result = await vector_service.vectorize_chunks(
                result.chunks,
                "test-doc-123",
                "test-location-456"
            )
            
            print(f"📈 Vectorization Result:")
            print(f"   Success: {vector_result.success}")
            print(f"   Total vectors: {vector_result.total_vectors}")
            print(f"   Successful vectors: {vector_result.successful_vectors}")
            print(f"   Failed vectors: {vector_result.failed_vectors}")
            print(f"   Processing time: {vector_result.processing_time_seconds:.2f}s")
            
            if vector_result.success:
                print("✅ Document processing and vectorization completed successfully!")
                
                # Test cleanup
                print("🧹 Testing vector cleanup...")
                cleanup_success = await vector_service.delete_document_vectors(
                    "test-doc-123",
                    "test-location-456"
                )
                print(f"   Cleanup success: {cleanup_success}")
            else:
                print(f"❌ Vectorization failed: {vector_result.error_message}")
        else:
            print(f"❌ Document processing failed: {result.error_message}")
        
        # Cleanup test file
        if test_file_path.exists():
            test_file_path.unlink()
            print("🗑️ Test file cleaned up")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Cleanup services
        await vector_service.cleanup()
        print("🧹 Services cleaned up")


async def test_configuration():
    """Test service configuration"""
    print("⚙️ Testing Configuration...")
    
    print(f"Service Name: {settings.SERVICE_NAME}")
    print(f"Service Version: {settings.SERVICE_VERSION}")
    print(f"Log Level: {settings.LOG_LEVEL}")
    print(f"Max File Size: {settings.MAX_FILE_SIZE_MB}MB")
    print(f"Chunk Size: {settings.CHUNK_SIZE}")
    print(f"Chunk Overlap: {settings.CHUNK_OVERLAP}")
    print(f"Batch Size: {settings.BATCH_SIZE}")
    print(f"Upload Directory: {settings.UPLOAD_DIR}")
    print(f"Allowed Extensions: {settings.ALLOWED_EXTENSIONS}")
    
    # Check required environment variables
    required_vars = ["OPENAI_API_KEY", "PINECONE_API_KEY", "PINECONE_ENVIRONMENT"]
    missing_vars = []
    
    for var in required_vars:
        try:
            value = getattr(settings, var)
            if not value:
                missing_vars.append(var)
            else:
                print(f"✅ {var}: {'*' * min(len(str(value)), 10)}")
        except Exception:
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {missing_vars}")
        return False
    else:
        print("✅ All required environment variables are set")
        return True


async def main():
    """Main test function"""
    print("🚀 Starting Document Processing Service Tests")
    print("=" * 50)
    
    # Test configuration first
    config_ok = await test_configuration()
    
    if config_ok:
        print("\n" + "=" * 50)
        await test_document_service()
    else:
        print("❌ Configuration test failed. Please check your environment variables.")
        return 1
    
    print("\n" + "=" * 50)
    print("🎉 All tests completed!")
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
