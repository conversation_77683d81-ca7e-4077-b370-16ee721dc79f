#!/usr/bin/env node

/**
 * Test script to check if ESLint issues are resolved
 * for the files we modified during the provider migration
 */

const { execSync } = require('child_process');
const path = require('path');

const filesToCheck = [
  'apps/platform/src/campaigns/CampaignEnqueueSendsJob.ts',
  'apps/platform/src/providers/push/PushJob.ts',
  'apps/platform/src/providers/email/EmailJob.ts',
  'apps/platform/src/providers/text/TextJob.ts',
  'apps/platform/src/providers/webhook/WebhookJob.ts',
  'apps/platform/src/render/TemplateService.ts',
  'apps/platform/src/services/GeneralInvitationService.ts',
  'apps/platform/src/auth/AdminInvitationService.ts',
];

console.log('🔍 Testing ESLint on modified files...\n');

let hasErrors = false;

for (const file of filesToCheck) {
  try {
    console.log(`Checking: ${file}`);
    
    // Run ESLint on the specific file
    const result = execSync(`cd apps/platform && npx eslint "${file.replace('apps/platform/', '')}" --format=compact`, {
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    if (result.trim()) {
      console.log(`❌ ESLint issues found in ${file}:`);
      console.log(result);
      hasErrors = true;
    } else {
      console.log(`✅ No ESLint issues in ${file}`);
    }
  } catch (error) {
    if (error.stdout && error.stdout.trim()) {
      console.log(`❌ ESLint issues found in ${file}:`);
      console.log(error.stdout);
      hasErrors = true;
    } else if (error.stderr && error.stderr.trim()) {
      console.log(`⚠️  ESLint error for ${file}:`);
      console.log(error.stderr);
      hasErrors = true;
    } else {
      console.log(`✅ No ESLint issues in ${file}`);
    }
  }
  console.log('');
}

if (hasErrors) {
  console.log('❌ Some files have ESLint issues that need to be fixed.');
  process.exit(1);
} else {
  console.log('🎉 All files pass ESLint checks!');
  process.exit(0);
}
